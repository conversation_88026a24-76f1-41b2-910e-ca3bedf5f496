import { getSupabaseServerClient } from '@kit/supabase/server-client';
import axios from 'axios';
import { NextRequest } from 'next/server';


export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json();
    // If imageUrl is provided, initiate animation
    console.log(body);
    if (body.imageUrl) {
      const response = await axios.post('https://api.smartberry.ai/generate_video_runway', {
        //image_url: "https://static.wixstatic.com/media/76ae51_14312bb7c3684e48a673ccffa877a20f~mv2.jpg/v1/fill/w_2024,h_1183,al_c/76ae51_14312bb7c3684e48a673ccffa877a20f~mv2.jpg",
        image_url: body.imageUrl,
        video_duration: 5,
        video_prompt: body.prompt || "Animate this image"
      });

      return new Response(JSON.stringify({ 
        taskId: response.data.task_id 
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // If taskId is provided, check status
    if (body.taskId) {
      const response = await axios.post(`https://api.smartberry.ai/get_status_and_path_video_runway`, {
        task_id: body.taskId
      });

      if (response.data.status === 'RUNNING' || response.data.status === 'PENDING' || response.data.status === 'THROTTLED') {
        return new Response(JSON.stringify({ status: 'RUNNING' }), {
          headers: { 'Content-Type': 'application/json' },
        });
      }

      if (response.data.status === 'SUCCEEDED') {
        const videoPath = response.data.video_path;
        console.log(videoPath);
        const videoResponse = await axios.post(`https://api.smartberry.ai/sb_download_video`, {
          video_path: videoPath,
        }, {
          responseType: 'arraybuffer',
        });
        console.log(videoResponse.data);
        const supabase = await getSupabaseServerClient();
        const fileName = `animated-images/${body.taskId}.mp4`;
        console.log(fileName);
        // const fileName = `generated/${company_id}/${crypto.randomUUID()}.${extension}`;
        const { error: uploadError } = await supabase.storage
          .from('generated')
          .upload(fileName, videoResponse.data, {
            contentType: 'video/mp4',
          });
        console.log(uploadError);
        if (uploadError) throw uploadError;
        const { data: { publicUrl } } = supabase.storage
          .from('generated')
          .getPublicUrl(fileName);
        console.log(publicUrl);
        return new Response(JSON.stringify({ 
          status: 'completed',
          videoUrl: publicUrl 
        }), {
          headers: { 'Content-Type': 'application/json' },
        });
      }

      if (response.data.status === 'FAILED') {
        console.log(response.data);
        return new Response(JSON.stringify({ status: 'FAILED' }), {
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    throw new Error('Invalid request - must provide either imageUrl or taskId');

  } catch (error) {
    console.error('Animation error:', error);
    return new Response(JSON.stringify({ error: 'Animation operation failed' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
