import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { fetchLinkedInAnalytics } from '../../../home/<USER>/analytics/_lib/server/analytics.service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const profileKey = searchParams.get('profileKey');

    if (!profileKey) {
      return NextResponse.json(
        { error: 'Profile key is required' },
        { status: 400 }
      );
    }

    // Verify user has access to this profile
    const supabase = getSupabaseServerClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has access to this profile
    const { data: profile } = await (supabase as any)
      .from('ayrshare_user_profile')
      .select('*')
      .eq('profileKey', profileKey)
      .eq('user_id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json(
        { error: 'Profile not found or access denied' },
        { status: 403 }
      );
    }

    const analyticsData = await fetchLinkedInAnalytics(profileKey);

    if (!analyticsData) {
      return NextResponse.json(
        { error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error('Error in analytics API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 