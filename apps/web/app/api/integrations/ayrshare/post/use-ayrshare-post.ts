'use client';

import { useState } from 'react';
import { postToAyrshare, postToLinkedIn } from './client';
import type { AyrsharePostRequest, AyrsharePostResponse } from './types';

interface UseAyrsharePostReturn {
  postToAyrshare: (data: AyrsharePostRequest) => Promise<AyrsharePostResponse>;
  postToLinkedIn: (
    post: string,
    companyId: string,
    options?: {
      mediaUrls?: string[];
      scheduleDate?: string;
      shortenLinks?: boolean;
      disableComments?: boolean;
      linkedInOptions?: {
        title?: string;
        description?: string;
      };
    }
  ) => Promise<AyrsharePostResponse>;
  loading: boolean;
  error: string | null;
}

/**
 * Hook for posting to social media via Ayrshare API
 * @returns Object with posting functions, loading state, and error state
 */
export function useAyrsharePost(): UseAyrsharePostReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePostToAyrshare = async (data: AyrsharePostRequest): Promise<AyrsharePostResponse> => {
    setLoading(true);
    setError(null);

    try {
      const result = await postToAyrshare(data);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const handlePostToLinkedIn = async (
    post: string,
    companyId: string,
    options: {
      mediaUrls?: string[];
      scheduleDate?: string;
      shortenLinks?: boolean;
      disableComments?: boolean;
      linkedInOptions?: {
        title?: string;
        description?: string;
      };
    } = {}
  ): Promise<AyrsharePostResponse> => {
    setLoading(true);
    setError(null);

    try {
      const result = await postToLinkedIn(post, companyId, options);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    postToAyrshare: handlePostToAyrshare,
    postToLinkedIn: handlePostToLinkedIn,
    loading,
    error,
  };
} 