import React, { useEffect, useState } from "react";
import { CompanyContent } from "~/types/company-content";
import { Card } from "@kit/ui/card";
import { Avatar } from "@kit/ui/avatar";
import { <PERSON><PERSON> } from "@kit/ui/button";
import { X } from "lucide-react";
import GeneralContentEditor from "./general-content-editor";
import { SocialProfile } from "../../index";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";

// Match the interface structure with the profileDetails from SocialsIntegrationCard
interface ProfileDetails {
  activeSocialAccounts: string[];
  displayNames: Array<{
    created: string;
    displayName: string;
    id: string;
    platform: string;
    profileUrl: string;
    userImage: string;
    username: string;
    headline?: string;
    subscriptionType?: string;
    verifiedType?: string;
    refreshDaysRemaining?: number;
    refreshRequired?: string;
    type?: string;
  }>;
  email: string | null;
  monthlyApiCalls: number;
  monthlyPostCount: number;
  refId: string;
  title: string;
  lastUpdated: string;
  nextUpdate: string;
}

// Component that contains the LinkedIn Preview content
export function LinkedInPreview({ selectedProfile }: {  selectedProfile?: SocialProfile | null }) {
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();

  const [companyContent] = useZeroQuery(
    zero.query.company_content,
    {
      ttl: '10m'
    }
  );
  
  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];  

  // Use Ayrshare hook for posting
  // const { postToLinkedIn, loading: posting, error: ayrshareError } = useAyrsharePost();
  
  const displayName = selectedProfile?.displayName || "Your Name";
  // @ts-expect-error - headline is not typed
  const headline = selectedProfile?.headline || "Your Professional Headline";
  const profilePicture = selectedProfile?.userImage || "/images/linked-in-default-profile-image.png";

  // Get typed image URLs
  const imageUrls = (selectedCompanyContent?.image_urls as string[]) || [];

  // Function to remove image from the array
  const removeImage = (urlToRemove: string) => {
    const currentImages = selectedCompanyContent?.image_urls as string[] || [];
    const updatedImages = currentImages.filter((url: string) => url !== urlToRemove);
    
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || "",
      values: {
        image_urls: updatedImages
      }
    });
  };

  return (
    <div className="mx-auto max-w-3xl p-6">
      
      <Card className="w-full p-4 bg-white shadow-sm border rounded-md overflow-hidden">
        {/* Post header with user info */}
        <div className="flex items-start space-x-3 mb-3">
          <Avatar className="h-12 w-12 rounded-full border">
            <img src={profilePicture} alt={displayName} />
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-[15px]">{displayName}</h3>
                <p className="text-gray-500 text-xs">{headline}</p>
                <div className="flex items-center text-xs text-gray-500 mt-1">
                  <span>Just now</span>
                  <span className="mx-1">•</span>
                  <span>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" data-supported-dps="16x16" fill="currentColor" className="mercado-match" width="16" height="16" focusable="false">
                    <path d="M8 1a7 7 0 107 7 7 7 0 00-7-7zM3 8a5 5 0 011-3l.55.55A1.5 1.5 0 015 6.62v1.07a.75.75 0 00.22.53l.56.56a.75.75 0 00.53.22H7v.69a.75.75 0 00.22.53l.56.56a.75.75 0 01.22.53V13a5 5 0 01-5-5zm6.24 4.83l2-2.46a.75.75 0 00.09-.8l-.58-1.16A.76.76 0 0010 8H7v-.19a.51.51 0 01.28-.45l.38-.19a.74.74 0 01.68 0L9 7.5l.38-.7a1 1 0 00.12-.48v-.85a.78.78 0 01.21-.53l1.07-1.09a5 5 0 01-1.54 9z"></path>
                  </svg>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Post content */}
        <div className="mb-4 whitespace-pre-line text-sm">
        <GeneralContentEditor companyContent={selectedCompanyContent as unknown as CompanyContent} />
        </div>
        {imageUrls.length > 0 && (
          <div className="mb-4">
            {imageUrls.length === 1 && (
              <div className="rounded-lg overflow-hidden border relative group">
                <img 
                  src={imageUrls[0]} 
                  alt="Post image"
                  className="w-full h-auto max-h-[400px] object-cover"
                />
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => imageUrls[0] && removeImage(imageUrls[0])}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
            {imageUrls.length === 2 && (
              <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden border">
                {imageUrls.map((url, index) => (
                  <div key={index} className="relative group">
                    <img 
                      src={url} 
                      alt={`Post image ${index + 1}`}
                      className="w-full h-[200px] object-cover"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => removeImage(url)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
            {imageUrls.length === 3 && (
              <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden border">
                <div className="relative group">
                  <img 
                    src={imageUrls[0]} 
                    alt="Post image 1"
                    className="w-full h-[200px] object-cover row-span-2"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => imageUrls[0] && removeImage(imageUrls[0])}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <div className="grid grid-rows-2 gap-1">
                  <div className="relative group">
                    <img 
                      src={imageUrls[1]} 
                      alt="Post image 2"
                      className="w-full h-[99px] object-cover"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-1 right-1 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => imageUrls[1] && removeImage(imageUrls[1])}
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </div>
                  <div className="relative group">
                    <img 
                      src={imageUrls[2]} 
                      alt="Post image 3"
                      className="w-full h-[99px] object-cover"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-1 right-1 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => imageUrls[2] && removeImage(imageUrls[2])}
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
            {imageUrls.length >= 4 && (
              <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden border">
                <div className="relative group">
                  <img 
                    src={imageUrls[0]} 
                    alt="Post image 1"
                    className="w-full h-[200px] object-cover"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => imageUrls[0] && removeImage(imageUrls[0])}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <div className="relative group">
                  <img 
                    src={imageUrls[1]} 
                    alt="Post image 2"
                    className="w-full h-[200px] object-cover"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => imageUrls[1] && removeImage(imageUrls[1])}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <div className="relative group">
                  <img 
                    src={imageUrls[2]} 
                    alt="Post image 3"
                    className="w-full h-[200px] object-cover"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => imageUrls[2] && removeImage(imageUrls[2])}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <div className="relative">
                  <div className="relative group">
                    <img 
                      src={imageUrls[3]} 
                      alt="Post image 4"
                      className="w-full h-[200px] object-cover"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                      onClick={() => imageUrls[3] && removeImage(imageUrls[3])}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                    {imageUrls.length > 4 && (
                      <div className="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center">
                        <span className="text-white text-2xl font-semibold">
                          +{imageUrls.length - 4}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Engagement stats */}
        <div className="flex justify-between items-center py-2 text-xs text-gray-500 border-t border-b border-gray-200">
          <div className="flex items-center space-x-1">
            <span className="flex space-x-1">
              <span className="rounded-full bg-blue-500 w-4 h-4 flex items-center justify-center text-white">
                👍
              </span>
            </span>
            <span>0 reactions</span>
          </div>
          <div>
            <span>0 comments</span>
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex justify-around pt-2">
          <button className="flex items-center space-x-1 text-gray-500 hover:bg-gray-100 rounded-md px-3 py-1.5 text-sm">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-supported-dps="24x24" fill="currentColor" className="mercado-match" width="24" height="24" focusable="false">
                <path d="M19.46 11l-3.91-3.91a7 7 0 01-1.69-2.74l-.49-1.47A2.76 2.76 0 0010.76 1 2.75 2.75 0 008 3.74v1.12a9.19 9.19 0 00.46 2.85L8.89 9H4.12A2.12 2.12 0 002 11.12a2.16 2.16 0 00.92 1.76A2.11 2.11 0 002 14.62a2.14 2.14 0 001.28 2 2 2 0 00-.28 1 2.12 2.12 0 002 2.12v.14A2.12 2.12 0 007.12 22h7.49a8.08 8.08 0 003.58-.84l.31-.16H21V11zM19 19h-1l-.73.37a6.14 6.14 0 01-2.69.63H7.72a1 1 0 01-1-.72l-.25-.87-.85-.41A1 1 0 015 17l.17-1-.76-.74A1 1 0 014.27 14l.66-1.09-.73-1.1a.49.49 0 01.08-.7.48.48 0 01.34-.11h7.05l-1.31-3.92A7 7 0 0110 4.86V3.75a.77.77 0 01.75-.75.75.75 0 01.71.51L12 5a9 9 0 002.13 3.5l4.5 4.5H19z"></path>
              </svg>
            </span>
            <span>Like</span>
          </button>
          <button className="flex items-center space-x-1 text-gray-500 hover:bg-gray-100 rounded-md px-3 py-1.5 text-sm">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-supported-dps="24x24" fill="currentColor" className="mercado-match" width="24" height="24" focusable="false">
                <path d="M7 9h10v1H7zm0 4h7v-1H7zm16-2a6.78 6.78 0 01-2.84 5.61L12 22v-4H8A7 7 0 018 4h8a7 7 0 017 7zm-2 0a5 5 0 00-5-5H8a5 5 0 000 10h6v2.28L19 15a4.79 4.79 0 002-4z"></path>
              </svg>
            </span>
            <span>Comment</span>
          </button>
          <button className="flex items-center space-x-1 text-gray-500 hover:bg-gray-100 rounded-md px-3 py-1.5 text-sm">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-supported-dps="24x24" fill="currentColor" className="mercado-match" width="24" height="24" focusable="false">
                <path d="M23 12l-4.61 7H16l4-6H8a3.92 3.92 0 00-4 3.84V17a4 4 0 00.19 1.24L5.12 21H3l-.73-2.22A6.4 6.4 0 012 16.94 6 6 0 018 11h12l-4-6h2.39z"></path>
              </svg>
            </span>
            <span>Share</span>
          </button>
        </div>
      </Card>
    </div>
  );
}
