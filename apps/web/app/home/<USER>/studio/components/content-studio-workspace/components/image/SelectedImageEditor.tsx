import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { Wand2, Edit, Undo2, GripHorizontal, Video } from "lucide-react";
import { Spinner } from "@kit/ui/spinner";
import { Input } from "@kit/ui/input";
import { Label } from "@kit/ui/label";
import { HelpCircle } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@kit/ui/tooltip";
import { useDrag } from 'react-dnd';
import './pintura.css'
// These imports are used by the image editor but conditionally loaded
// import { PinturaEditor } from "@pqina/react-pintura";
// import { getEditorDefaults, createDefaultFontFamilyOptions, createMarkupEditorShapeStyleControls } from "@pqina/pintura";
import { EditImageDialog } from './EditImageDialog';
import { uploadEditedImage } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useImageContent, useVideoContent } from '../../context/ContentStudioContext';


// Define the type for our draggable item
interface DragItem {
  type: 'animated-video';
  videoUrl: string;
}

const DraggableVideo: React.FC<{ videoUrl: string }> = ({ videoUrl }) => {
  const [{ isDragging }, dragRef, previewRef] = useDrag<DragItem, unknown, { isDragging: boolean }>(() => ({
    type: 'animated-video',
    item: { type: 'animated-video', videoUrl },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const handlePreviewRef = (element: HTMLDivElement | null) => {
    if (previewRef) {
      (previewRef as (element: HTMLDivElement | null) => void)(element);
    }
  };

  const handleDragRef = (element: HTMLDivElement | null) => {
    if (dragRef) {
      (dragRef as (element: HTMLDivElement | null) => void)(element);
    }
  };

  return (
    <div 
      ref={handlePreviewRef}
      className={`relative group ${isDragging ? 'opacity-50' : ''}`}
    >
      <div 
        ref={handleDragRef}
        className="absolute top-2 left-2 z-10 bg-background/80 p-1 rounded-md cursor-move opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <GripHorizontal className="h-4 w-4" />
      </div>
      <video 
        src={videoUrl} 
        className="w-full h-full object-cover"
        controls
        loop
        autoPlay
        muted
      />
    </div>
  );
};

export const SelectedImageEditor: React.FC = () => {
  const workspace = useTeamAccountWorkspace();
  const [isAnimating, setIsAnimating] = useState(false);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [_taskId, setTaskId] = useState<string | null>(null); // Used in setTaskId but not directly used
  const [isSaving, setIsSaving] = useState(false);
  const [animationPrompt, setAnimationPrompt] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const {
    selectedImageUrl,
    selectedEditorImage
  } = useImageContent();
  const { setSelectedEditorVideo } = useVideoContent();
  const [currentImageUrl, setCurrentImageUrl] = useState(selectedEditorImage || selectedImageUrl);
  // Update currentImageUrl when imageUrl prop changes
  React.useEffect(() => {
    const currentImageUrl = selectedEditorImage || selectedImageUrl;
    setCurrentImageUrl(currentImageUrl);
  }, [selectedEditorImage, selectedImageUrl ]);

  const checkAnimationStatus = async (taskId: string) => {
    try {
      const response = await fetch('/api/ai/animate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskId }),
      });

      const data = await response.json();
      
      if (data.status === 'RUNNING' || data.status === 'PENDING' || data.status === 'THROTTLED') {
        // Check again in 2 seconds
        setTimeout(() => checkAnimationStatus(taskId), 2000);
      } else if (data.status === 'completed') {
        setVideoUrl(data.videoUrl);
        setIsAnimating(false);
      } else if (data.status === 'FAILED') {
        setIsAnimating(false);
        alert('Animation failed. Please try again.');
      }
    } catch (error) {
      console.error('Animation status check error:', error);
      setIsAnimating(false);
      alert('Error checking animation status');
    }
  };

  const handleAnimate = async () => {
    try {
      setIsAnimating(true);
      const response = await fetch('/api/ai/animate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          imageUrl: currentImageUrl,
          prompt: animationPrompt || "Animate this image"
        }),
      });

      const data = await response.json();
      
      if (data.taskId) {
        setTaskId(data.taskId);
        checkAnimationStatus(data.taskId);
      } else {
        throw new Error('No task ID received');
      }
    } catch (error) {
      console.error('Animation error:', error);
      setIsAnimating(false);
      alert('Error starting animation');
    }
  };

  const handleUndo = () => {
    setVideoUrl(null);
    setTaskId(null);
    setIsAnimating(false);
  };

  const handleEditImage = async (blob: Blob) => {
    try {
      setIsSaving(true);
      
      // Convert the blob to a File object
      const file = new File([blob], 'edited-image.png', { type: 'image/png' });
      
      // Upload the edited image
      const result = await uploadEditedImage(file, workspace.account.id);
      
      // Update the current image URL
      setCurrentImageUrl(result.url);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving edited image:', error);
      alert('Failed to save edited image');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInsertVideo = () => {
    if (videoUrl) {
      setSelectedEditorVideo(videoUrl);
    }
  };

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold mb-4">Selected Image Options</h3>
      
      <div className="relative w-full aspect-video mb-6 rounded-lg overflow-hidden">
        {videoUrl ? (
          <DraggableVideo videoUrl={videoUrl} />
        ) : (
          <img
            src={currentImageUrl || ''}
            alt="Selected image"
            className="w-full h-full object-cover"
          />
        )}
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="animation-prompt">Image Prompt</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Enter a prompt for how you want this image to be animated</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            id="animation-prompt"
            placeholder="E.g., Gentle waves moving left to right"
            value={animationPrompt}
            onChange={(e) => setAnimationPrompt(e.target.value)}
            disabled={isAnimating || !!videoUrl}
          />
        </div>

        <div className="flex flex-wrap gap-4">
          <Button
            variant="outline"
            className="flex items-center justify-center gap-2 min-w-[100px] px-3 flex-1"
            onClick={handleAnimate}
            disabled={isAnimating || !!videoUrl}
          >
            {isAnimating ? (
              <>
                <Spinner className="h-4 w-4" />
                <span>Animating...</span>
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4" />
                <span>Animate</span>
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            className="flex items-center justify-center gap-2 min-w-[100px] px-3 flex-1"
            onClick={() => setIsEditing(true)}
            disabled={isAnimating || !!videoUrl}
          >
            {isSaving ? (
              <>
                <Spinner className="h-4 w-4" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </>
            )}
          </Button>

          {videoUrl && (
            <>
              <Button
                variant="outline"
                className="flex items-center justify-center gap-2 min-w-[100px] px-3 flex-1"
                onClick={handleUndo}
              >
                <Undo2 className="h-4 w-4" />
                <span>Undo</span>
              </Button>
              
              <Button
                variant="default"
                className="flex items-center justify-center gap-2 min-w-[100px] px-3 flex-1"
                onClick={handleInsertVideo}
              >
                <Video className="h-4 w-4" />
                <span>Insert Video</span>
              </Button>
            </>
          )}
        </div>
      </div>

      <EditImageDialog
        imageUrl={currentImageUrl || ''}
        isOpen={isEditing}
        onClose={() => setIsEditing(false)}
        onSave={handleEditImage}
      />
    </div>
  );
}; 