'use client';

import { <PERSON><PERSON> } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Badge } from '@kit/ui/badge';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Plus, MessageSquare, Calendar, Users, Target } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string;
  research_type: 'pain-points' | 'trending-topics' | 'recent-news';
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface ResearchSidebarProps {
  selectedResearchId?: string;
  onSelectResearch: (research: GeneratedResearch | null) => void;
  onNewResearch: () => void;
}

function useGeneratedResearch(accountId: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['generated-research', accountId],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('generated_research')
          .select('*')
          .eq('account_id', accountId)
          .order('created_at', { ascending: false });

        if (error) {
          // If table doesn't exist yet, return empty array
          if (error.message.includes('relation "public.generated_research" does not exist')) {
            return [];
          }
          throw new Error(`Failed to fetch research history: ${error.message}`);
        }

        return (data || []) as GeneratedResearch[];
      } catch (error) {
        // Handle case where table doesn't exist yet
        console.warn('Generated research table not available yet:', error);
        return [];
      }
    },
    enabled: !!accountId,
  });
}

const RESEARCH_TYPE_LABELS = {
  'pain-points': 'Pain Points',
  'trending-topics': 'Trending Topics',
  'recent-news': 'Recent News'
} as const;

const RESEARCH_TYPE_ICONS = {
  'pain-points': '🎯',
  'trending-topics': '📈',
  'recent-news': '📰'
} as const;

export function ResearchSidebar({ 
  selectedResearchId, 
  onSelectResearch, 
  onNewResearch 
}: ResearchSidebarProps) {
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';

  const { data: researchHistory = [], isLoading } = useGeneratedResearch(accountId);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const generateTitle = (research: GeneratedResearch) => {
    const typeLabel = RESEARCH_TYPE_LABELS[research.research_type];
    return research.title || `${typeLabel} Research`;
  };

  return (
    <div className="w-80 border-r bg-muted/30 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <Button
          onClick={onNewResearch}
          className="w-full justify-start gap-2"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
          <Trans i18nKey="marketResearch:newResearch" defaults="New Research" />
        </Button>
      </div>

      {/* Research History */}
      <div className="flex-1 overflow-hidden">
        <div className="p-4 pb-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            <Trans i18nKey="marketResearch:researchHistory" defaults="Research History" />
          </h3>
        </div>

        <ScrollArea className="flex-1 px-2">
          {isLoading ? (
            <div className="p-4 space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded-lg animate-pulse" />
              ))}
            </div>
          ) : researchHistory.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <Trans 
                i18nKey="marketResearch:noHistory" 
                defaults="No research history yet. Start by creating your first research session." 
              />
            </div>
          ) : (
            <div className="space-y-1 pb-4">
              {researchHistory.map((research) => (
                <Card
                  key={research.id}
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:bg-accent/50",
                    selectedResearchId === research.id && "bg-accent border-primary/50"
                  )}
                  onClick={() => onSelectResearch(research)}
                >
                  <CardContent className="p-3 space-y-2">
                    {/* Title and Type */}
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium truncate">
                            {research.topic || RESEARCH_TYPE_LABELS[research.research_type]}
                        </h4>
                        <Badge variant="outline" className="text-xs mt-1">
                          {RESEARCH_TYPE_ICONS[research.research_type]} {generateTitle(research)}
                        </Badge>
                      </div>
                    </div>

                    {/* Metadata */}
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{research.time_filter}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <Target className="h-3 w-3" />
                            <span>{research.results?.length || 0}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            <span>{research.content_suggestions?.length || 0}</span>
                          </div>
                        </div>
                        <span>{formatDate(research.created_at)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
} 