'use client';

import { useState, useTransition, useEffect } from 'react';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { ExternalLink, Star, Loader2 } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useUser } from '@kit/supabase/hooks/use-user';

import { saveResearchAction, removeSavedResearchAction } from '../_lib/server-actions';
import { If } from '@kit/ui/if';

const TIME_FILTERS = [
  'Last 3 months',
  'Last 6 months', 
  'Last 12 months'
];

const RESEARCH_TYPES = [
  { value: 'pain-points', label: 'Pain Points' },
  { value: 'trending-topics', label: 'Trending Topics' },
  { value: 'recent-news', label: 'Recent News' }
];

interface FormData {
  icpId: string;
  personaId: string;
  timeFilter: string;
  type: string;
  topic: string;
}

interface ResearchResult {
  title: string;
  description: string;
  source: string;
  source_url: string;
  relevance_score: number;
}

interface ContentSuggestion {
  topic: string;
  description: string;
  content_type: string;
  target_audience: string;
}

interface ICP {
  id: string;
  name: string;
}

interface Persona {
  id: string;
  name: string;
  role: string;
}

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: 'pain-points' | 'trending-topics' | 'recent-news';
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface MarketResearchFormProps {
  selectedResearch?: GeneratedResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
}

function useICPs(accountId: string) {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ['icps', accountId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('icps')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch ICPs: ${error.message}`);
      }

      return data as ICP[];
    },
    enabled: !!accountId,
  });
}

function usePersonas(accountId: string, icpId?: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['personas', accountId, icpId],
    queryFn: async () => {

      let query = supabase
        .from('personas')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      // Filter by ICP if selected
      if (icpId) {
        query = query.eq('icp_id', icpId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch personas: ${error.message}`);
      }

      return data as Persona[];
    },
    enabled: !!accountId,
  });
}

export function MarketResearchForm({ selectedResearch, onResearchSaved }: MarketResearchFormProps) {
  const [pending, startTransition] = useTransition();
  const [savePending, setSavePending] = useState<string | null>(null);
  const [results, setResults] = useState<ResearchResult[]>([]);
  const [contentSuggestions, setContentSuggestions] = useState<ContentSuggestion[]>([]);
  const [savedItems, setSavedItems] = useState<Set<string>>(new Set());
  const [savedSuggestions, setSavedSuggestions] = useState<Set<string>>(new Set());
  const [currentResearchId, setCurrentResearchId] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({
    icpId: '',
    personaId: '',
    timeFilter: '',
    type: '',
    topic: ''
  });

  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const { data: user } = useUser();
  const zero = useZero();

  // Watch for real-time updates to the current research
  const [liveResearch] = useZeroQuery(
    currentResearchId && zero 
      ? zero.query.generated_research.where('id', currentResearchId).one()
      : zero?.query.generated_research.where('id', 'never-match').one()
  );

  const { data: icps = [], isLoading: icpsLoading } = useICPs(accountId);
  const { data: personas = [], isLoading: personasLoading } = usePersonas(accountId, formData.icpId);

  // Update results when live research data changes
  useEffect(() => {
    if (liveResearch && liveResearch.id === currentResearchId) {
      console.log('Live research updated:', liveResearch);
      
      // Update results and content suggestions from the live data
      if (liveResearch.results && Array.isArray(liveResearch.results)) {
        setResults(liveResearch.results as ResearchResult[]);
      }
      
      if (liveResearch.content_suggestions && Array.isArray(liveResearch.content_suggestions)) {
        setContentSuggestions(liveResearch.content_suggestions as ContentSuggestion[]);
      }

      // If research is no longer generating, show success message
      if (liveResearch.is_generating === false && results.length === 0 && 
          liveResearch.results && Array.isArray(liveResearch.results) && liveResearch.results.length > 0) {
        const contentSuggestionsCount = liveResearch.content_suggestions && Array.isArray(liveResearch.content_suggestions) 
          ? liveResearch.content_suggestions.length 
          : 0;
        toast.success('Market research completed!', {
          description: `Found ${liveResearch.results.length} research insights and ${contentSuggestionsCount} content suggestions.`
        });
      }
    }
  }, [liveResearch, currentResearchId, results.length]);

  // Load selected research data when selectedResearch changes
  useEffect(() => {
    if (selectedResearch) {
      setFormData({
        icpId: selectedResearch.icp_id,
        personaId: selectedResearch.persona_id || 'no-persona',
        timeFilter: selectedResearch.time_filter,
        type: selectedResearch.research_type,
        topic: selectedResearch.topic || '',
      });
      setResults(selectedResearch.results || []);
      setContentSuggestions(selectedResearch.content_suggestions || []);
      setCurrentResearchId(selectedResearch.id);
      setSavedItems(new Set());
      setSavedSuggestions(new Set());
    } else {
      // Reset form for new research
      setFormData({
        icpId: '',
        personaId: '',
        timeFilter: '',
        type: '',
        topic: ''
      });
      setResults([]);
      setContentSuggestions([]);
      setCurrentResearchId(null);
      setSavedItems(new Set());
      setSavedSuggestions(new Set());
    }
  }, [selectedResearch]);

  // Generate a unique key for each research item
  const getResearchKey = (result: ResearchResult) => {
    return `${formData.icpId}-${formData.personaId === 'no-persona' ? 'no-persona' : formData.personaId}-${result.title}-${formData.type}`;
  };

  // Generate a unique key for each content suggestion
  const getSuggestionKey = (suggestion: ContentSuggestion) => {
    return `${formData.icpId}-${formData.personaId === 'no-persona' ? 'no-persona' : formData.personaId}-${suggestion.topic}-suggestion`;
  };

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: '' // Reset persona when ICP changes
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handleTimeFilterChange = (value: string) => {
    setFormData(prev => ({ ...prev, timeFilter: value }));
  };

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }));
  };

  const handleTopicChange = (value: string) => {
    setFormData(prev => ({ ...prev, topic: value }));
  };

  // Helper function to parse comma-separated sources and URLs
  const parseSourcesAndUrls = (sources: string | null | undefined, urls: string | null | undefined) => {
    // Handle null/undefined cases by defaulting to empty strings
    const sourceArray = (sources || '').split(',').map(s => s.trim()).filter(Boolean);
    const urlArray = (urls || '').split(',').map(u => u.trim()).filter(Boolean);
    
    // Pair sources with URLs, handling cases where counts might not match
    return sourceArray.map((source, index) => ({
      source,
      url: urlArray[index] || undefined
    }));
  };

  const handleSaveToggle = (result: ResearchResult) => {
    const key = getResearchKey(result);
    const isSaved = savedItems.has(key);
    
    setSavePending(key);

    startTransition(async () => {
      try {
        if (isSaved) {
          // Remove from saved items - skip if no persona since server action requires persona
          if (formData.personaId && formData.personaId !== 'no-persona') {
            await removeSavedResearchAction({
              accountId,
              icpId: formData.icpId,
              personaId: formData.personaId,
              title: result.title,
              researchType: formData.type as 'pain-points' | 'trending-topics' | 'recent-news',
            });
          }

          setSavedItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(key);
            return newSet;
          });

          toast.success('Research item removed from saved items');
        } else {
          zero.mutate.saved_research.insert({
            id: crypto.randomUUID(),
            account_id: accountId,
            icp_id: formData.icpId,
            persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
            research_type: formData.type as 'pain-points' | 'trending-topics' | 'recent-news',
            time_filter: formData.timeFilter,
            title: result.title,
            topic: formData.topic || '',
            description: result.description,
            source: result.source,
            source_url: result.source_url,
          });

          setSavedItems(prev => new Set(prev).add(key));
          toast.success('Research item saved successfully!');
        }
      } catch (error) {
        console.error('Error toggling save state:', error);
        toast.error('Failed to update saved state', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      } finally {
        setSavePending(null);
      }
    });
  };

  const handleSaveSuggestionToggle = (suggestion: ContentSuggestion) => {
    const key = getSuggestionKey(suggestion);
    const isSaved = savedSuggestions.has(key);
    
    setSavePending(key);

    startTransition(async () => {
      try {
        if (isSaved) {
          // Remove from saved suggestions
          // You might want to create a separate action for content suggestions
          // For now, I'll use a similar pattern to research items
          setSavedSuggestions(prev => {
            const newSet = new Set(prev);
            newSet.delete(key);
            return newSet;
          });

          toast.success('Content suggestion removed from saved items');
        } else {
          // Save suggestion
          setSavedSuggestions(prev => new Set(prev).add(key));
          toast.success('Content suggestion saved successfully!');
        }
      } catch (error) {
        console.error('Error toggling save state:', error);
        toast.error('Failed to update saved state', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      } finally {
        setSavePending(null);
      }
    });
  };
  
  const handleSubmit = () => {
    if (!isFormValid || !zero || !user) return;

    startTransition(async () => {
      try {
        // Get the full ICP and persona data
        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        // const selectedPersona = personas.find(persona => persona.id === formData.personaId);
        
        // Generate a title for the research
        const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);
        const researchTitle = `${selectedType?.label || 'Research'} - ${selectedICP?.name || 'Unknown ICP'}`;

        // Generate a unique ID for this research
        const researchId = crypto.randomUUID();

        console.log('Creating market research with Zero mutation...');

        // Use Zero mutation to create the research record
        zero.mutate.generated_research.insert({
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type,
          time_filter: formData.timeFilter,
          title: researchTitle,
          topic: formData.topic,
          created_by: user.id,
        });

        // Clear previous results and show loading state
        setResults([]);
        setContentSuggestions([]);
        setSavedItems(new Set());
        setSavedSuggestions(new Set());
        setCurrentResearchId(researchId);

        // Call the onResearchSaved callback with a placeholder record
        const placeholderResearch: GeneratedResearch = {
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type as 'pain-points' | 'trending-topics' | 'recent-news',
          time_filter: formData.timeFilter,
          title: researchTitle,
          results: [],
          content_suggestions: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          topic: formData.topic,
        };

        onResearchSaved?.(placeholderResearch);
        
        toast.success('Market research generation started!', {
          description: 'Your research is being generated in the background. Results will appear shortly.'
        });

      } catch (error) {
        console.error('Error generating market research:', error);
        toast.error('Failed to generate market research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.timeFilter && formData.type;
  const isGenerating = liveResearch?.is_generating === true;

  const selectedICP = icps.find(icp => icp.id === formData.icpId);
  const selectedPersona = formData.personaId === 'no-persona' ? null : personas.find(persona => persona.id === formData.personaId);
  const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);

  return (
    <div className="space-y-6">
      {/* Show research title if viewing existing research */}
      {selectedResearch && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold">{selectedResearch.title}</h2>
          <p className="text-sm text-muted-foreground">
            Created on {new Date(selectedResearch.created_at).toLocaleDateString()}
          </p>
        </div>
      )}

      {/* Show generating status */}
      {isGenerating && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">Research Generation in Progress</p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Your market research is being generated. Results will appear here automatically.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Research Configuration Form - only show when creating new research */}
      {!selectedResearch && (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Generate New Research</CardTitle>
            <CardDescription>Fill in the form fields below to get insights about your ICP or persona relevant to your business.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* ICP Selection */}
            <div className="space-y-2">
              <Label htmlFor="icp-select">Ideal Customer Profile (ICP)</Label>
              <Select 
                value={formData.icpId} 
                onValueChange={handleICPChange} 
                disabled={icpsLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder={icpsLoading ? "Loading ICPs..." : "Select an ICP"} />
                </SelectTrigger>
                <SelectContent>
                  {icps.map(icp => (
                    <SelectItem key={icp.id} value={icp.id}>
                      {icp.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Persona Selection */}
            <div className="space-y-2">
              <Label htmlFor="persona-select">
                Persona <span className="text-muted-foreground">(Optional)</span>
              </Label>
              <Select 
                value={formData.personaId} 
                onValueChange={handlePersonaChange} 
                disabled={personasLoading || !formData.icpId}
              >
                <SelectTrigger>
                  <SelectValue 
                    placeholder={
                      !formData.icpId 
                        ? "Select an ICP first" 
                        : personasLoading 
                          ? "Loading personas..." 
                          : "Select a persona (optional)"
                    } 
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-persona">
                    <span className="text-muted-foreground">No specific persona</span>
                  </SelectItem>
                  {personas.map(persona => (
                    <SelectItem key={persona.id} value={persona.id}>
                      <div className="flex flex-col">
                        <span>{persona.name}</span>
                        <span className="text-xs text-muted-foreground">{persona.role}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Topic Input */}
            <div className="space-y-2">
              <Label htmlFor="topic-input">Topic</Label>
              <Input
                id="topic-input"
                type="text"
                placeholder="Enter research topic or question about ICP or persona, or leave blank for general insights."
                value={formData.topic}
                onChange={(e) => handleTopicChange(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Research Type */}
            <div className="space-y-3">
              <Label>Type</Label>
              <RadioGroup 
                value={formData.type} 
                onValueChange={handleTypeChange} 
                className="grid grid-cols-1 gap-3"
              >
                {RESEARCH_TYPES.map(type => (
                  <div key={type.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={type.value} id={type.value} />
                    <Label htmlFor={type.value} className="cursor-pointer">
                      {type.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            {/* Time Filter */}
            <div className="space-y-2">
              <Label htmlFor="time-filter">Time Filter</Label>
              <Select 
                value={formData.timeFilter} 
                onValueChange={handleTimeFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select time period" />
                </SelectTrigger>
                <SelectContent>
                  {TIME_FILTERS.map(filter => (
                    <SelectItem key={filter} value={filter}>
                      {filter}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Submit Button */}
            <Button 
              onClick={handleSubmit}
              disabled={!isFormValid || pending || !zero || !user || isGenerating}
              className="w-full"
              size="lg"
            >
              {pending || isGenerating ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Generating Research...
                </div>
              ) : (
                'Generate Market Research'
              )}
            </Button>

            {/* Selected Configuration Summary */}
            {(selectedICP || selectedPersona || selectedType) && (
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <h4 className="font-medium text-sm">Research Configuration:</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedICP && (
                    <Badge variant="outline">ICP: {selectedICP.name}</Badge>
                  )}
                  {selectedPersona && (
                    <Badge variant="outline">Persona: {selectedPersona.name}</Badge>
                  )}
                  {selectedType && (
                    <Badge variant="outline">Type: {selectedType.label}</Badge>
                  )}
                  {formData.timeFilter && (
                    <Badge variant="outline">Period: {formData.timeFilter}</Badge>
                  )}
                  {formData.topic && (
                    <Badge variant="outline">Topic: {formData.topic}</Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Research Results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Research Results</h3>
            <Badge variant="outline">{results.length} insights</Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {results.map((result, index) => {
              const key = getResearchKey(result);
              const isSaved = savedItems.has(key);
              const isPending = savePending === key;
              
              return (
                <Card key={index} className="h-full relative">
                  {/* Star button in top right corner */}
                  <button
                    onClick={() => handleSaveToggle(result)}
                    disabled={isPending || !formData.icpId}
                    className={cn(
                      "absolute top-3 right-3 p-1.5 rounded-full transition-all duration-200",
                      "hover:bg-muted focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                      "disabled:opacity-50 disabled:cursor-not-allowed"
                    )}
                    title={isSaved ? "Remove from saved" : "Save research item"}
                  >
                    <Star 
                      className={cn(
                        "h-4 w-4 transition-colors duration-200",
                        isSaved 
                          ? "fill-yellow-400 text-yellow-400" 
                          : "text-muted-foreground hover:text-foreground",
                        isPending && "animate-pulse"
                      )} 
                    />
                  </button>

                  <CardContent className="p-4 space-y-3 pr-12">
                    <div className="space-y-2">
                      <h4 className="font-bold text-sm leading-tight">{result.title}</h4>
                      <Badge 
                        variant={result.relevance_score >= 8 ? 'default' : result.relevance_score >= 6 ? 'secondary' : 'outline'}
                        className="text-xs"
                      >
                        Score: {result.relevance_score}/10
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-foreground leading-relaxed">
                      {result.description}
                    </p>
                    
                    {/* Sources */}
                    <div className="text-xs text-muted-foreground mt-auto space-y-1">
                      <div className="font-medium text-muted-foreground/80 mb-1">Sources</div>
                      {parseSourcesAndUrls(result.source, result.source_url).map((sourceItem, sourceIndex) => (
                        <div key={sourceIndex} className="flex items-center gap-2">
                          <If condition={!!sourceItem.url} fallback={
                            <span>{sourceItem.source}</span>
                          }>
                            <a 
                              href={sourceItem.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-primary hover:underline"
                            >
                              {sourceItem.source}
                            </a>
                            <Button
                              variant="ghost"
                              size="sm"
                              asChild
                              className="h-4 w-4 p-0"
                            >
                              <a 
                                href={sourceItem.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                title="Open source"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </Button>
                          </If>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Content Suggestions */}
      {contentSuggestions.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Content Suggestions</h3>
            <Badge variant="outline">{contentSuggestions.length} suggestions</Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contentSuggestions.map((suggestion, index) => {
              const key = getSuggestionKey(suggestion);
              const isSaved = savedSuggestions.has(key);
              const isPending = savePending === key;
              
              return (
                <Card key={index} className="h-full relative">
                  {/* Star button in top right corner */}
                  <button
                    onClick={() => handleSaveSuggestionToggle(suggestion)}
                    disabled={isPending || !formData.icpId}
                    className={cn(
                      "absolute top-3 right-3 p-1.5 rounded-full transition-all duration-200",
                      "hover:bg-muted focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                      "disabled:opacity-50 disabled:cursor-not-allowed"
                    )}
                    title={isSaved ? "Remove from saved" : "Save content suggestion"}
                  >
                    <Star 
                      className={cn(
                        "h-4 w-4 transition-colors duration-200",
                        isSaved 
                          ? "fill-yellow-400 text-yellow-400" 
                          : "text-muted-foreground hover:text-foreground",
                        isPending && "animate-pulse"
                      )} 
                    />
                  </button>

                  <CardContent className="p-4 space-y-3 pr-12">
                    <div className="space-y-2">
                      <h4 className="font-bold text-sm leading-tight">{suggestion.topic}</h4>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="secondary" className="text-xs">
                          {suggestion.content_type}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-sm text-foreground leading-relaxed">
                      {suggestion.description}
                    </p>
                    
                    <div className="mt-auto pt-2">
                      <p className="text-xs text-muted-foreground">
                        <span className="font-medium">Target:</span> {suggestion.target_audience}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
} 