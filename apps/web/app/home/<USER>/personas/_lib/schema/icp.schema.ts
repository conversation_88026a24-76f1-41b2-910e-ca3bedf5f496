import { z } from 'zod';
import { 
  GEOGRAPHY_MARKETS_OPTIONS,
  DECISION_MAKING_DEPARTMENTS_OPTIONS,
  LifecycleStage
} from '~/types/icp';

const CustomFieldSchema = z.object({
  id: z.string(),
  label: z.string().min(1, 'Field label is required'),
  type: z.enum(['text', 'textarea', 'number', 'numberrange', 'select', 'multiselect']),
  value: z.any(),
  options: z.array(z.string()).optional(), // for select/multiselect types
});

const NumberRangeSchema = z.object({
  min: z.number().nullable(),
  max: z.number().nullable(),
}).refine(
  (data) => {
    if (data.min !== null && data.max !== null) {
      return data.min <= data.max;
    }
    return true;
  },
  {
    message: "Minimum value must be less than or equal to maximum value",
  }
);

// Schema for the data field that contains all ICP attributes
const ICPDataSchema = z.object({
  // All fields are optional - users will pick which ones they need
  target_industries: z.array(z.string()).optional(),
  geography_markets: z.array(z.enum(GEOGRAPHY_MARKETS_OPTIONS as [string, ...string[]])).optional(),
  use_cases_problems: z.array(z.string()).optional(),
  buying_triggers: z.array(z.string()).optional(),
  decision_making_departments: z.array(z.string()).optional(),
  red_flags_disqualifiers: z.array(z.string()).optional(),
  technologies_used: z.array(z.string()).optional(),
  content_needs: z.array(z.string()).optional(),
  
  // Range fields
  company_size_employees: NumberRangeSchema.optional(),
  company_revenue_range_usd: NumberRangeSchema.optional(),
  typical_contract_value: NumberRangeSchema.optional(),
  
  // Enum field
  lifecycle_stage: z.enum(['Startup', 'Scale-up', 'Mature Enterprise'] as [LifecycleStage, ...LifecycleStage[]]).nullable().optional(),
  
  // Notes
  notes: z.string().optional(),
  
  // Custom fields
  custom_fields: z.array(CustomFieldSchema).optional(),
}).strict();

export const CreateICPSchema = z.object({
  name: z.string().min(1, 'ICP name is required').max(255, 'Name is too long'),
  data: ICPDataSchema.default({}),
});

export const UpdateICPSchema = CreateICPSchema.extend({
  id: z.string().min(1, 'ICP ID is required'),
});

// For form compatibility, we'll also export a flattened type that matches the form structure
export const CreateICPFormSchema = z.object({
  name: z.string().min(1, 'ICP name is required').max(255, 'Name is too long'),
  
  // All fields are optional - users will pick which ones they need
  target_industries: z.array(z.string()).optional(),
  geography_markets: z.array(z.enum(GEOGRAPHY_MARKETS_OPTIONS as [string, ...string[]])).optional(),
  use_cases_problems: z.array(z.string()).optional(),
  buying_triggers: z.array(z.string()).optional(),
  decision_making_departments: z.array(z.string()).optional(),
  red_flags_disqualifiers: z.array(z.string()).optional(),
  technologies_used: z.array(z.string()).optional(),
  content_needs: z.array(z.string()).optional(),
  
  // Range fields
  company_size_employees: NumberRangeSchema.optional(),
  company_revenue_range_usd: NumberRangeSchema.optional(),
  typical_contract_value: NumberRangeSchema.optional(),
  
  // Enum field
  lifecycle_stage: z.enum(['Startup', 'Scale-up', 'Mature Enterprise'] as [LifecycleStage, ...LifecycleStage[]]).nullable().optional(),
  
  // Notes
  notes: z.string().optional(),
  
  // Custom fields - made optional to match form usage
  custom_fields: z.array(CustomFieldSchema).optional(),
});

export type CreateICPSchemaType = z.infer<typeof CreateICPSchema>;
export type UpdateICPSchemaType = z.infer<typeof UpdateICPSchema>;
export type CreateICPFormSchemaType = z.infer<typeof CreateICPFormSchema>;
export type ICPDataType = z.infer<typeof ICPDataSchema>;
export type CustomField = z.infer<typeof CustomFieldSchema>; 