'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';
import { SocialProfile } from './analytics-client-wrapper';


interface ProfileSelectorProps {
  profiles: SocialProfile[];
  selectedProfileKey?: string;
  onProfileChange: (profileKey: string) => void;
}

export function ProfileSelector({ profiles, selectedProfileKey, onProfileChange }: ProfileSelectorProps) {
  if (profiles.length === 0) {
    return null;
  }

  if (profiles.length === 1) {
    const firstProfile = profiles[0];
    if (!firstProfile) return null;
    console.log({firstProfile})
    return (
      <div className="text-sm text-muted-foreground">
        <p>{firstProfile.profile_name}</p>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">
        <Trans i18nKey="analytics:selectProfile" defaults="Profile:" />
      </span>
      <Select value={selectedProfileKey} onValueChange={onProfileChange}>
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Select a profile" />
        </SelectTrigger>
        <SelectContent>
          {profiles.map((profile) => (
            <SelectItem key={profile.id} value={profile.profileKey}>
              {profile.profile_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
} 