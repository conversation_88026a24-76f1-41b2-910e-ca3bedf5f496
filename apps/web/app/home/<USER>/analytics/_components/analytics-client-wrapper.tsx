'use client';

import { useState } from 'react';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AlertCircle } from 'lucide-react';

import { AnalyticsHeader } from './analytics-header';
import { AnalyticsOverview } from './analytics-overview';
import { PostAnalyticsList } from './post-analytics-list';
import { ProfileSelector } from './profile-selector';

export interface SocialProfile {
  id: string;
  title: string;
  profileKey: string;
  user_id: string;
  company_id: string;
  created_at: string;
  profile_name: string;
}

interface AnalyticsClientWrapperProps {
  profiles: SocialProfile[];
  initialAnalyticsData: any;
  initialProfileKey: string;
}

export function AnalyticsClientWrapper({ 
  profiles, 
  initialAnalyticsData, 
  initialProfileKey 
}: AnalyticsClientWrapperProps) {
  const [selectedProfileKey, setSelectedProfileKey] = useState(initialProfileKey);
  const [selectedProfile, setSelectedProfile] = useState(profiles.find(profile => profile.profileKey === selectedProfileKey));
  const [analyticsData, setAnalyticsData] = useState(initialAnalyticsData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleProfileChange = async (profileKey: string) => {
    if (profileKey === selectedProfileKey) return;
    
    setSelectedProfileKey(profileKey);
    setSelectedProfile(profiles.find(profile => profile.profileKey === profileKey));
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/analytics/linkedin?profileKey=${encodeURIComponent(profileKey)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch analytics data');
      }
      
      const newAnalyticsData = await response.json();
      setAnalyticsData(newAnalyticsData);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <AnalyticsHeader
        title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
        description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
      >
        <ProfileSelector
          profiles={profiles}
          selectedProfileKey={selectedProfileKey}
          onProfileChange={handleProfileChange}
        />
      </AnalyticsHeader>

      <PageBody>
        <div className="flex flex-col gap-8">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
              </AlertTitle>
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          ) : analyticsData ? (
            <>
              <AnalyticsOverview data={analyticsData} />
              <PostAnalyticsList profileId={selectedProfile?.id} posts={analyticsData.posts} />
            </>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
              </AlertTitle>
              <AlertDescription>
                <Trans i18nKey="analytics:errorDescription" defaults="Unable to fetch LinkedIn analytics data. Please try again later." />
              </AlertDescription>
            </Alert>
          )}
        </div>
      </PageBody>
    </>
  );
} 