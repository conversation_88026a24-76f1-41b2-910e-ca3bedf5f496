// import { getSupabaseServerClient } from "@kit/supabase/server-client";
// import { createTeamAccountsApi } from "@kit/team-accounts/api";
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AlertCircle } from 'lucide-react';

import { AnalyticsHeader } from './_components/analytics-header';
import { fetchLinkedInAnalytics } from './_lib/server/analytics.service';
import { AnalyticsClientWrapper } from './_components/analytics-client-wrapper';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
import { getSocialProfiles } from '../integrations/_lib/server/server-actions';
import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';

interface AnalyticsPageProps {
  params: Promise<{ account: string }>;
  searchParams?: { type?: string };
}

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('analytics:pageTitle');

  return {
    title,
  };
};

async function AnalyticsPage(props: AnalyticsPageProps) {
  const params = await props.params;
  
  // Load the team workspace data
  const workspace = await loadTeamWorkspace(params.account);
  
  // Now you have access to:
  const user = workspace.user;           // Current logged-in user
  const account = workspace.account;     // Current team account (company)
  
  const profiles = await getSocialProfiles(user.id, account.id);
  
  // If no profiles exist, show an error message
  if (!profiles || profiles.length === 0) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:noProfilesTitle" defaults="No Social Profiles Found" />
            </AlertTitle>
            <AlertDescription>
              <Trans i18nKey="analytics:noProfilesDescription" defaults="You need to create and connect a social profile before viewing analytics. Please visit the integrations page to set up your social profiles." />
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // Use the first profile as default
  const defaultProfile = profiles[0];
  if (!defaultProfile) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
            </AlertTitle>
            <AlertDescription>
              <Trans i18nKey="analytics:errorDescription" defaults="Unable to fetch LinkedIn analytics data. Please try again later." />
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // Fetch initial analytics data for the default profile
  const initialAnalyticsData = await fetchLinkedInAnalytics(defaultProfile.profileKey);
  console.log(JSON.stringify(initialAnalyticsData, null, 2));
  
  return (
    <AnalyticsClientWrapper
      profiles={profiles}
      initialAnalyticsData={initialAnalyticsData}
      initialProfileKey={defaultProfile.profileKey}
    />
  );
}

export default withI18n(AnalyticsPage);