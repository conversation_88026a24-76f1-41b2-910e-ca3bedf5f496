'use client';

import { useEffect, useRef, useState } from 'react';

import { useMutation } from '@tanstack/react-query';
import BulletList from '@tiptap/extension-bullet-list';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Globe, Loader2, Plus, Upload, X } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Brand } from '~/types/brand';
import { getLocalApi } from '~/utils/api.util';

// Vercel Blob import - you'll need to install @vercel/blob
// pnpm add @vercel/blob
import { upload } from '@vercel/blob/client';

const isValidUrl = (url: string) => {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

const RichTextEditor = ({
  content,
  onChange,
  label,
}: {
  content: string;
  onChange: (html: string) => void;
  label: string;
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
      }),
      BulletList,
      OrderedList,
      ListItem,
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class:
          'min-h-[80px] w-full rounded-medium bg-default-100 hover:bg-default-200 focus:bg-default-100 outline-none p-3 text-sm',
      },
    },
  });

  // Add useEffect to update editor content when prop changes
  useEffect(() => {
    if (editor && editor.getHTML() !== content) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  if (!editor) return null;

  return (
    <div className="relative w-full">
      <label className="text-foreground block pb-1.5 text-sm font-medium">
        {label}
      </label>
      <EditorContent className="border" editor={editor} />
    </div>
  );
};

const AVAILABLE_FIELDS = [
  { key: 'value_proposition', label: 'Brand Value Proposition' },
  { key: 'audience', label: 'Brand Audience' },
  { key: 'personality', label: 'Brand Personality' },
  { key: 'messaging_pillars', label: 'Brand Messaging Pillars' },
  { key: 'identity', label: 'Brand Identity' },
  { key: 'guidelines', label: 'Brand Guidelines' },
  { key: 'voice', label: 'Brand Voice' },
];

interface BrandDetailsFormProps {
  onDetailsChange: (details: any) => void;
  onSubmit?: () => void;
  isLoading: boolean;
  initialValues?: any;
}

const extractAndProcessText = async (file: File) => {
  try {
    // Upload file to Vercel Blob
    const blob = await upload(file.name, file, {
      access: 'public',
      handleUploadUrl: '/api/documents/upload',
    });

    // Process the uploaded file using the blob URL
    const response = await getLocalApi().post('/documents/process', {
      blobUrl: blob.url,
    });

    if (response.status !== 200) {
      throw new Error('Failed to extract text from file');
    }

    const extractedText = response.data;
    if (!extractedText) {
      throw new Error('No text content extracted from document');
    }

    // Convert extracted text to brand brief format
    const brandInfoResponse = await getLocalApi().post(
      '/ai/extract-brand-info-from-text',
      {
        brand_text: JSON.stringify(extractedText),
        brand_name: 'Company',
      },
    );

    if (!brandInfoResponse.status || brandInfoResponse.status !== 200) {
      throw new Error('Failed to extract brand information');
    }

    const brandInfo = brandInfoResponse.data;
    console.log({ brandInfo });
    return brandInfo;
  } catch (error) {
    console.error('Error processing document:', error);
    throw error;
  }
};

export function BrandDetailsForm({
  onDetailsChange,
  onSubmit,
  isLoading,
  initialValues,
}: BrandDetailsFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [activeFields, setActiveFields] = useState<string[]>([
    'mission',
    'vision',
  ]);
  const [formData, setFormData] = useState<any>({
    mission: '',
    vision: '',
  });
  const [isUrlModalOpen, setIsUrlModalOpen] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [isScrapingLoading, setIsScrapingLoading] = useState(false);
  const [urlError, setUrlError] = useState('');
  const [showWarning, setShowWarning] = useState(true);

  useEffect(() => {
    if (initialValues) {
      try {
        // Calculate active fields based on parsed data
        const newActiveFields = ['mission', 'vision'];
        Object.entries(initialValues).forEach(([key, value]) => {
          if (value && key !== 'mission' && key !== 'vision') {
            newActiveFields.push(key);
          }
        });

        // Update states
        setActiveFields(newActiveFields);
        setFormData(initialValues);
        onDetailsChange(initialValues); // Send the raw format to parent
      } catch (error) {
        console.error('Error parsing company brand information:', error);
      }
    }
  }, [initialValues, onDetailsChange]);

  const extractMutation = useMutation({
    mutationFn: extractAndProcessText,
    onSuccess: (brandInfo) => {
      console.log({ brandInfo });
      try {
        const parsedData = JSON.parse(brandInfo) as Brand;

        // Calculate new active fields based on parsed data
        const newActiveFields = ['mission', 'vision'];
        Object.entries(parsedData).forEach(([key, value]) => {
          if (value && key !== 'mission' && key !== 'vision') {
            newActiveFields.push(key);
          }
        });

        // Update states in a single batch
        setActiveFields(newActiveFields);
        setFormData(parsedData);
        onDetailsChange(parsedData);
        setShowWarning(true);
        
        toast.success('Brand information extracted successfully from document');
      } catch (error) {
        console.error('Error parsing brand information:', error);
        toast.error('Failed to parse brand information from document');
      }
    },
    onError: (error) => {
      console.error('Error processing document:', error);
      toast.error('Failed to extract brand information from document');
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      toast.error('Please upload a PDF file');
      return;
    }

    // Reset the file input to allow the same file to be uploaded again
    event.target.value = '';
    extractMutation.mutate(file);
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setWebsiteUrl(url);

    if (!url) {
      setUrlError('');
    } else if (!url.startsWith('https://')) {
      setUrlError('URL must start with https://');
    } else if (!isValidUrl(url)) {
      setUrlError('Please enter a valid URL');
    } else {
      setUrlError('');
    }
  };

  const handleWebsiteScrape = async () => {
    if (!websiteUrl || !isValidUrl(websiteUrl)) return;

    setIsScrapingLoading(true);
    try {
      // First scrape the website
      const scrapeResponse = await getLocalApi().post('/scrape-website', {
        url: websiteUrl,
      });

      if (!scrapeResponse.status || scrapeResponse.status !== 200) {
        throw new Error('Failed to scrape website');
      }
      const scrapedData = scrapeResponse.data;

      const brandInfoResponse = await getLocalApi().post(
        '/ai/extract-brand-info-from-text',
        {
          brand_text: scrapedData.text,
          brand_name: 'Company',
        },
      );
      const brandInfo = await brandInfoResponse.data;
      const parsedData = brandInfo;

      const newActiveFields = ['mission', 'vision'];
      Object.entries(parsedData).forEach(([key, value]) => {
        if (value && key !== 'mission' && key !== 'vision') {
          newActiveFields.push(key);
        }
      });

      setActiveFields(newActiveFields);
      setFormData(parsedData);
      onDetailsChange(brandInfo); // Send the raw format to parent
      setIsUrlModalOpen(false);
      setWebsiteUrl('');
      setShowWarning(true);
    } catch (error) {
      console.error('Error processing website:', error);
    } finally {
      setIsScrapingLoading(false);
    }
  };

  const handleFieldAdd = (fieldKey: string) => {
    setActiveFields([...activeFields, fieldKey]);
    setFormData({ ...formData, [fieldKey]: '' });
  };

  const handleFieldRemove = (fieldKey: string) => {
    setActiveFields(activeFields.filter((field) => field !== fieldKey));
    const newData: any = { ...formData };
    delete newData[fieldKey];
    setFormData(newData);
    onDetailsChange(newData);
  };

  const handleInputChange = (key: string, value: string) => {
    console.log({ key, value });
    const newData = { ...formData, [key]: value };
    setFormData(newData);
    onDetailsChange(newData);
  };

  const renderField = (key: string, label: string) => (
    <div className="relative">
      <RichTextEditor
        content={formData[key] || ''}
        onChange={(value) => handleInputChange(key, value)}
        label={label}
      />
      {key !== 'mission' && key !== 'vision' && (
        <Button
          size="sm"
          variant="default"
          color="danger"
          className="absolute -top-2 -right-2"
          onClick={() => handleFieldRemove(key)}
        >
          <X size={16} />
        </Button>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <div className="mb-4 flex gap-2">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileUpload}
          accept=".pdf"
          className="hidden"
        />
        <Button variant="default" onClick={() => fileInputRef.current?.click()}>
          {extractMutation.isPending && (
            <Loader2 className="mr-2 animate-spin" />
          )}
          <Upload size={16} className="mr-2" />
          Upload Document
        </Button>
        <Button variant="default" onClick={() => setIsUrlModalOpen(true)}>
          <Globe size={16} className="mr-2" />
          Pull From Website
        </Button>
      </div>

      {/* URL Modal */}
      <Dialog open={isUrlModalOpen} onOpenChange={setIsUrlModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enter Website URL</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="text-warning mb-4 text-sm">
              This feature helps you get started quickly by extracting content
              from a website. However, it should not be considered a replacement
              for a proper brand brief.
            </p>
            <Input
              placeholder="https://example.com"
              value={websiteUrl}
              onChange={handleUrlChange}
              className={urlError ? 'border-red-500' : ''}
            />
            {urlError && (
              <p className="mt-1 text-sm text-red-500">{urlError}</p>
            )}
            <p className="text-xs text-gray-500">
              URL must start with https://
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUrlModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleWebsiteScrape}
              disabled={!websiteUrl || !!urlError}
            >
              {isScrapingLoading ? 'Extracting...' : 'Extract Content'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Warning Message */}
      {showWarning && (
        <div className="rounded-medium bg-blue-50 p-4">
          <p className="text-sm text-blue-700">
            The content below was automatically generated from your uploaded
            document/website. Please review and edit to ensure accuracy and
            completeness of your brand information or upload your existing brand
            brief.
          </p>
        </div>
      )}

      {/* Required Fields */}
      <div className="space-y-4">
        {renderField('mission', 'Brand Mission')}
        {renderField('vision', 'Brand Vision')}
      </div>

      {/* Optional Fields */}
      <div className="space-y-4">
        {activeFields.map((field) => {
          if (field === 'mission' || field === 'vision') return null;
          const fieldDef = AVAILABLE_FIELDS.find((f) => f.key === field);
          if (!fieldDef) return null;

          return <div key={field}>{renderField(field, fieldDef.label)}</div>;
        })}
      </div>

      {/* Add Field Buttons */}
      <div className="flex flex-wrap gap-2">
        {AVAILABLE_FIELDS.map((field) => {
          if (activeFields.includes(field.key)) return null;
          return (
            <Button
              key={field.key}
              variant="default"
              onClick={() => handleFieldAdd(field.key)}
            >
              <Plus size={16} className="mr-2" />
              Add {field.label}
            </Button>
          );
        })}
      </div>

      {/* Submit Button */}
      {onSubmit && (
        <Button color="primary" onClick={onSubmit} className="mt-8">
          {isLoading && <Loader2 size={16} className="mr-2 animate-spin" />}
          Finish Setup
        </Button>
      )}
    </div>
  );
}
