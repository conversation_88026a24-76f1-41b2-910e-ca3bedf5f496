'use client';

import { useEffect, useState } from 'react';

import { Plus } from 'lucide-react';

import { Button } from '@kit/ui/button';

import { getUniqueId } from '~/services/utils';

interface FontPreview {
  name: string;
  url: string;
  fontFamily: string;
}

interface FontUploadSectionProps {
  onFontsChange: (fonts: FontPreview[]) => void;
}

export const FontUploadSection: React.FC<FontUploadSectionProps> = ({
  onFontsChange,
}) => {
  const [fonts, setFonts] = useState<FontPreview[]>([]);
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleFontUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        const uniqueFontFamily = `font-family-${getUniqueId()}-${Math.random().toString(36).substr(2, 9)}`;

        const fontFace = new FontFace(uniqueFontFamily, `url(${dataUrl})`);
        fontFace
          .load()
          .then(() => {
            document.fonts.add(fontFace);
            setFonts((prev) => [
              ...prev,
              {
                name: file.name,
                url: dataUrl,
                fontFamily: uniqueFontFamily,
              },
            ]);
          })
          .catch((err) => console.error('Error loading font:', err));
      };
      reader.readAsDataURL(file);
    });
  };

  const handleRemoveFont = (indexToRemove: number) => {
    setFonts((prevFonts) => {
      const newFonts = prevFonts.filter((_, index) => index !== indexToRemove);
      return newFonts;
    });
  };

  const fetchWebsiteFonts = async () => {
    if (!websiteUrl) return;
    setIsLoading(true);

    try {
      const response = await fetch(`/api/fonts?url=${websiteUrl}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Process each font URL
      for (const url of data.fonts) {
        const response = await fetch(url);
        const blob = await response.blob();
        const reader = new FileReader();

        reader.onload = (e) => {
          const dataUrl = e.target?.result as string;
          const uniqueFontFamily = `font-family-${getUniqueId()}-${Math.random().toString(36).substr(2, 9)}`;

          const fontFace = new FontFace(uniqueFontFamily, `url(${dataUrl})`);
          fontFace
            .load()
            .then(() => {
              document.fonts.add(fontFace);
              setFonts((prev) => [
                ...prev,
                {
                  name: url.split('/').pop() || 'Unknown Font',
                  url: dataUrl,
                  fontFamily: uniqueFontFamily,
                },
              ]);
            })
            .catch((err) => console.error('Error loading font:', err));
        };

        reader.readAsDataURL(blob);
      }
    } catch (error) {
      console.error('Error fetching fonts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    onFontsChange(fonts);
  }, [fonts, onFontsChange]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="relative inline-block">
          <Button type="button">
            <Plus className="h-4 w-4" />
            Add Font
          </Button>
          <input
            type="file"
            accept=".ttf,.otf,.woff,.woff2"
            onChange={handleFontUpload}
            multiple
            className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          />
        </div>

        <div className="flex items-center gap-2">
          <input
            type="url"
            value={websiteUrl}
            onChange={(e) => setWebsiteUrl(e.target.value)}
            placeholder="Enter website URL"
            className="rounded border px-3 py-2"
          />
          <Button
            type="button"
            onClick={fetchWebsiteFonts}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Check website for fonts'}
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {fonts.map((font, index) => (
          <div
            key={index}
            className="flex items-center justify-between rounded border p-4"
          >
            <div className="flex-1">
              <p className="text-sm text-gray-500">{font.name}</p>
              <p style={{ fontFamily: font.fontFamily }}>
                The quick brown fox jumps over the lazy dog
              </p>
            </div>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleRemoveFont(index)}
            >
              Remove
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
