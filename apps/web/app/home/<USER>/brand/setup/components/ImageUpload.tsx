'use client';

import React, { useState } from 'react';

import Image from 'next/image';

import { ImagePlus } from 'lucide-react';
import { useDropzone } from 'react-dropzone';

import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';

interface ImageUploadProps {
  onChange: (
    images: Array<{ file: File; preview: string; name: string }>,
  ) => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({ onChange }) => {
  const [images, setImages] = useState<
    Array<{ file: File; preview: string; name: string }>
  >([]);

  // Helper to update images and notify parent
  const updateImages = (
    newImages: Array<{ file: File; preview: string; name: string }>,
  ) => {
    setImages(newImages);
    onChange(newImages);
  };

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      const readFiles = acceptedFiles.map((file) => {
        return new Promise<{ file: File; preview: string; name: string }>(
          (resolve) => {
            const reader = new FileReader();
            reader.onload = () => {
              resolve({
                file,
                preview: reader.result as string,
                name: file.name,
              });
            };
            reader.readAsDataURL(file);
          },
        );
      });

      Promise.all(readFiles).then((newImages) => {
        updateImages([...images, ...newImages]);
      });
    },
    [images, updateImages],
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
    },
    multiple: true, // Explicitly enable multiple file selection
  });

  const handleNameChange = (index: number, newName: string) => {
    updateImages(
      images.map((img, i) => (i === index ? { ...img, name: newName } : img)),
    );
  };

  const removeImage = (index: number) => {
    updateImages(images.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Upload Logos</h1>
      <div
        {...getRootProps()}
        className="cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400"
      >
        <input {...getInputProps()} />
        <ImagePlus className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2">
          Drag & drop images here, or click to select files
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
        {images.map((image, index) => (
          <div key={index} className="relative">
            <div className="relative h-32 w-full">
              <Image
                src={image.preview}
                alt={image.name}
                fill
                className="rounded-lg object-contain"
                sizes="(max-width: 768px) 50vw, 33vw"
              />
            </div>
            <Input
              value={image.name}
              onChange={(e) => handleNameChange(index, e.target.value)}
              className="mt-2 text-sm"
            />
            <Button
              color="danger"
              size="sm"
              className="absolute top-2 right-2"
              onClick={() => removeImage(index)}
            >
              ✕
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
