'use client';

// import { containerObject } from "@syncfusion/ej2-base";
import { useState } from 'react';

import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';

import { Brand } from '~/types/brand';

interface ColorUploadSelectionsProps {
  onColorsChange: (colors: string[]) => void;
  brandDetails: Brand;
}

export const ColorUploadSelections: React.FC<ColorUploadSelectionsProps> = ({
  onColorsChange,
  brandDetails,
}) => {
  const [colors, setColors] = useState<string[]>(
    brandDetails.brand_colors || [],
  );
  const [currentColor, setCurrentColor] = useState<string>('');
  console.log({ brandDetails });
  const isValidColor = (color: string) => {
    const s = new Option().style;
    s.color = color;
    return s.color !== '';
  };

  const convertToHex = (color: string): string => {
    // Create a temporary div to use the browser's color parsing
    const div = document.createElement('div');
    div.style.color = color;
    document.body.appendChild(div);

    // Get computed style
    const computed = window.getComputedStyle(div).color;
    document.body.removeChild(div);

    // Parse RGB values
    const match = computed.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (!match) return color; // Return original if parsing fails

    // Convert RGB to hex
    const hex =
      '#' +
      match
        .slice(1)
        .map((n) => {
          const hex = parseInt(n).toString(16);
          return hex.length === 1 ? '0' + hex : hex;
        })
        .join('');

    return hex;
  };

  const handleColorAdd = () => {
    if (currentColor && isValidColor(currentColor)) {
      const hexColor = convertToHex(currentColor);
      const newColors = [...colors, hexColor];
      setColors(newColors);
      onColorsChange(newColors);
      setCurrentColor('');
    } else toast.error('Invalid color');
  };

  const handleColorRemove = (index: number) => {
    const newColors = colors.filter((_, i) => i !== index);
    setColors(newColors);
    onColorsChange(newColors);
  };

  return (
    <div className="flex max-w-96 flex-col gap-4">
      <div className="flex gap-2">
        <Input
          value={currentColor}
          onChange={(e) => setCurrentColor(e.target.value)}
          placeholder="Enter color (hex, rgb, or hsl)"
          className="flex-1"
        />
        <Button onClick={handleColorAdd}>Add</Button>
      </div>

      <div className="flex flex-col gap-2">
        {colors.map((color, index) => (
          <div key={index} className="flex items-center gap-4">
            <div
              className="h-10 w-10 rounded-md"
              style={{ backgroundColor: color }}
            />
            <span className="flex-1">{color}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleColorRemove(index)}
            >
              Remove
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
