'use server';

import { getLogger } from '@kit/shared/logger';

import { createCompanyBrand, updateBrandDetails } from '~/services/brand';
import { Brand, BrandInfoInput } from '~/types/brand';

export async function completeBrandUpload(
  brandColors: string[],
  companyId: string,
  brandDetails?: Brand,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'complete-brand-upload',
  };

  logger.info(ctx, 'Starting brand upload completion...');

  if (!brandDetails) {
    logger.error(
      { ...ctx, error: 'Missing brand details' },
      'Brand details are missing',
    );
    throw new Error('Brand details are missing');
  }

  try {

    // Format brand input data
    const brandInput: BrandInfoInput = {
      ...brandDetails,
      company_id: companyId,
      brand_colors: [{
        name: 'Primary Colors',
        colors: brandColors.map(hex => ({ name: hex, hex }))
      }],
      is_draft: false,
    } as BrandInfoInput;

    let result;

    // Determine if we're creating or updating based on ID presence
    if (brandDetails.id) {
      logger.info(
        { ...ctx, brandId: brandDetails.id },
        'Updating existing brand',
      );
      result = await updateBrandDetails(companyId, brandDetails.id, brandInput);
    } else {
      logger.info({ ...ctx, companyId }, 'Creating new brand');
      result = await createCompanyBrand(brandInput);
    }

    logger.info(
      { ...ctx, brandId: result?.id },
      'Brand upload completed successfully',
    );
    return { success: true, data: result };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error completing brand upload');
    return { success: false, error };
  }
}
