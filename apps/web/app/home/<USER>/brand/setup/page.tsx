'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { useMutation } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';

import RowSteps from '~/components/row-steps';
import { uploadBrandFiles, uploadFontFiles } from '~/services/storage';
import { Brand } from '~/types/brand';

import { completeBrandUpload } from './_lib/server-actions';
import { BrandDetailsForm } from './components/BrandDetailsForm';
import { ColorUploadSelections } from './components/ColorUploadSelections';
import { FontUploadSection } from './components/FontUploadSection';
import { ImageUpload } from './components/ImageUpload';

const steps = [
  { title: 'Brand Details' },
  { title: 'Upload Logos' },
  { title: 'Brand Identity' },
];

interface FontPreview {
  name: string;
  url: string;
  fontFamily: string;
}

export default function SetupPage() {
  const router = useRouter();
  const { account } = useTeamAccountWorkspace();
  const [currentStep, setCurrentStep] = useState(0);
  const [logos, setLogos] = useState<
    Array<{ file: File; preview: string; name: string }>
  >([]);
  const [fonts, setFonts] = useState<FontPreview[]>([]);
  const [colors, setColors] = useState<string[]>([]);
  const [brandDetails, setBrandDetails] = useState<Brand>({
    mission: '',
    vision: '',
    voice: '',
    value_proposition: '',
    audience: '',
    messaging_pillars: '',
    identity: '',
    guidelines: '',
    personality: '',
    product_list: '',
    brand_colors: [],
    brand_fonts: [],
    company_id: '',
  });

  const canProceedToNext = (step: number) => {
    switch (step) {
      case 0:
        console.log({ brandDetails });
        return Object.values(brandDetails).every((value) => value !== '');
      // return true;
      case 1:
        // return logos.length > 0;
        return true;
      case 2:
        // return colors.length > 0 && fonts.length > 0;
        return true;
      default:
        return false;
    }
  };

  const saveBrandMutation = useMutation({
    mutationFn: async () => {
      console.log({ brandDetails, logos, fonts, colors });
      await uploadBrandFiles(logos, account?.id);
      await uploadFontFiles(fonts, account?.id);
      console.log('Files uploaded', brandDetails);
      return await completeBrandUpload(colors, account?.id, brandDetails);
    },
    onSuccess: () => {
      router.push(`/home/<USER>/brand/`);
      router.refresh();
    },
  });

  const handleSave = () => {
    saveBrandMutation.mutate();
  };

  const handleNext = () => {
    if (canProceedToNext(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-8">
            <BrandDetailsForm
              onDetailsChange={setBrandDetails}
              isLoading={saveBrandMutation.isPending}
            />
            <Button
              color="primary"
              onClick={handleNext}
              disabled={!canProceedToNext(0)}
            >
              Next
            </Button>
          </div>
        );
      case 1:
        return (
          <div className="space-y-4">
            <ImageUpload onChange={setLogos} />
            <Button
              color="primary"
              onClick={handleNext}
              disabled={!canProceedToNext(1)}
            >
              Next
            </Button>
          </div>
        );
      case 2:
        return (
          <div className="space-y-8">
            <FontUploadSection onFontsChange={setFonts} />
            <ColorUploadSelections
              brandDetails={brandDetails}
              onColorsChange={setColors}
            />
            <Button
              color="primary"
              onClick={handleSave}
              disabled={!canProceedToNext(2)}
            >
              {saveBrandMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Complete Setup
            </Button>
          </div>
        );
    }
  };

  return (
    <div className="container mx-auto space-y-6 p-6">
      <h1 className="text-2xl font-bold">Brand Setup</h1>
      <div className="flex justify-center">
        <RowSteps
          steps={steps}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
        />
      </div>
      {renderStep()}
    </div>
  );
}
