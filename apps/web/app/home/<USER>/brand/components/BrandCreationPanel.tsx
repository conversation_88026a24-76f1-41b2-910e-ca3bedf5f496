'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@kit/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { toast } from 'sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import { BrandDetailsForm } from '../setup/components/BrandDetailsForm';
import { createCompanyBrand } from '~/services/brand';
import { Brand, BrandColorPalette, BrandFont } from '~/types/brand';

export function BrandCreationPanel() {
  const queryClient = useQueryClient();
  const workspace = useTeamAccountWorkspace();
  const [brandDetails, setBrandDetails] = useState<Brand>({
    mission: '',
    vision: '',
    voice: '',
    value_proposition: '',
    audience: '',
    messaging_pillars: '',
    identity: '',
    guidelines: '',
    personality: '',
    product_list: '',
    brand_colors: [] as BrandColorPalette[],
    brand_fonts: [] as BrandFont[],
    company_id: workspace.account.id,
  });

  const createBrandMutation = useMutation({
    mutationFn: async () => {
      return await createCompanyBrand({
        ...brandDetails,
        company_id: workspace.account.id,
      });
    },
    onSuccess: () => {
      toast.success('Brand created successfully');
      queryClient.invalidateQueries({ queryKey: ['brand', workspace.account.id] });
    },
    onError: (error) => {
      toast.error('Failed to create brand');
      console.error('Error creating brand:', error);
    },
  });

  const handleDetailsChange = (newDetails: Brand) => {
    setBrandDetails({
      ...newDetails,
      company_id: workspace.account.id,
    });
  };

  const handleCreateBrand = () => {
    createBrandMutation.mutate();
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Create Your Brand</CardTitle>
          <CardDescription>
            Add your brand information to help generate content that matches your brand voice and style
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="manual" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
              <TabsTrigger value="import">Import Content</TabsTrigger>
            </TabsList>
            
            <TabsContent value="manual">
              <div className="space-y-6">
                <p className="text-sm text-muted-foreground">
                  Fill in your brand details to get started. At minimum, add your mission and vision statements.
                </p>
                <BrandDetailsForm 
                  onDetailsChange={handleDetailsChange}
                  isLoading={createBrandMutation.isPending}
                />
                <Button 
                  onClick={handleCreateBrand}
                  disabled={!brandDetails.mission || !brandDetails.vision || createBrandMutation.isPending}
                >
                  Create Brand
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="import">
              <div className="space-y-6">
                <p className="text-sm text-muted-foreground">
                  Import your brand information from an existing document or website. 
                  Our AI will extract key brand details automatically.
                </p>
                <BrandDetailsForm 
                  onDetailsChange={handleDetailsChange}
                  isLoading={createBrandMutation.isPending}
                />
                <Button 
                  onClick={handleCreateBrand}
                  disabled={!brandDetails.mission || !brandDetails.vision || createBrandMutation.isPending}
                >
                  Create Brand
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}