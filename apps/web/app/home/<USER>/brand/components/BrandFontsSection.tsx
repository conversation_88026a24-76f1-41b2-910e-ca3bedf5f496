'use client';

import React from 'react';
import BrandSection from './BrandSection';
import BrandFonts from './font/BrandFonts';
import { useBrand } from './BrandContext';
import ErrorBoundary from './ErrorBoundary';

export default function BrandFontsSection() {
  const { getBrandFonts, updateBrandSection, uploadedFonts } = useBrand();
  
  const fonts = getBrandFonts();
  
  return (
    <ErrorBoundary>
      <BrandSection 
        title="Fonts" 
        value="brand_fonts" 
        id="fonts-section"
      >
        <BrandFonts
          fonts={fonts}
          onSave={(fonts) => updateBrandSection('brand_fonts', fonts)}
          uploadedFonts={uploadedFonts}
        />
      </BrandSection>
    </ErrorBoundary>
  );
}
