'use client';

import { Accordion } from '@kit/ui/accordion';
import { Button } from '@kit/ui/button';
import { ConfirmDialog } from '~/components/confirm-dialog';
import { Brand, UploadedFont } from '~/types/brand';

import { BrandProvider, useBrand } from './BrandContext';
import BrandSidebar from './BrandSidebar';
import BrandDetailsSection from './BrandDetailsSection';
import BrandColorsSection from './BrandColorsSection';
import BrandFontsSection from './BrandFontsSection';
import BrandLogosSection from './BrandLogosSection';
import BrandAdvancedSection from './BrandAdvancedSection';
import ErrorBoundary from './ErrorBoundary';

// Import custom styles
import './BrandStyles.css';

interface BrandDisplayProps {
  brand: Brand & { id: string };
  logos: Array<{ name: string; url: string }>;
  uploadedFonts: Array<UploadedFont>;
  refetchBrand: () => void;
  isNewBrand?: boolean;
}

// This is the dialog component that's used by the context
const BrandDialogs = () => {
  const { 
    isDeleteDialogOpen, 
    setIsDeleteDialogOpen, 
    handleDeleteBrand,
    isDeletePaletteDialogOpen, 
    setIsDeletePaletteDialogOpen,
    confirmDeletePalette
  } = useBrand();
  
  return (
    <>
      <ConfirmDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Brand"
        description="Are you sure you want to delete your brand? This will permanently remove all brand details, logos, fonts, and colors. This action cannot be undone."
        confirmText="Delete Everything"
        onConfirm={handleDeleteBrand}
        variant="danger"
      />

      <ConfirmDialog
        isOpen={isDeletePaletteDialogOpen}
        onOpenChange={setIsDeletePaletteDialogOpen}
        title="Delete Color Palette"
        description="Are you sure you want to delete this color palette? This action cannot be undone."
        confirmText="Delete"
        onConfirm={confirmDeletePalette}
        variant="danger"
      />
    </>
  );
};

// This is the inner content that uses the context
const BrandDisplayContent = () => {
  const { isNewBrand, createBrand } = useBrand();
  
  return (
    <div className="brand-main-container">
      <BrandSidebar />
      <div className="brand-content-area">
        {isNewBrand && (
          <div className="mb-4 bg-blue-50 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div>
                <h3 className="text-lg font-medium text-blue-800">Create Your Brand</h3>
                <p className="text-sm text-blue-600">
                  Add your brand details below, then click &quot;Save Brand&quot; to create your brand profile.
                </p>
              </div>
              <Button 
                onClick={createBrand}
                className="whitespace-nowrap"
              >
                Save Brand
              </Button>
            </div>
          </div>
        )}
        
        <Accordion
          type="single"
          collapsible
          className="w-full"
          defaultValue="brand_details"
        >
          <BrandDetailsSection />
          <BrandColorsSection />
          <BrandFontsSection />
          <BrandLogosSection />
          {!isNewBrand && <BrandAdvancedSection />}
        </Accordion>
        
        <BrandDialogs />
      </div>
    </div>
  );
};

// This is the main component that provides the context
export function BrandDisplay({
  brand: initialBrand,
  logos: initialLogos,
  uploadedFonts,
  refetchBrand,
  isNewBrand = false,
}: BrandDisplayProps) {
  return (
    <ErrorBoundary>
      <BrandProvider
        initialBrand={initialBrand}
        initialLogos={initialLogos}
        initialUploadedFonts={uploadedFonts}
        refetchBrand={refetchBrand}
        isNewBrand={isNewBrand}
      >
        <BrandDisplayContent />
      </BrandProvider>
    </ErrorBoundary>
  );
}
