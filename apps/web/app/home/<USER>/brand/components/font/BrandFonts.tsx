import React, { useEffect, useState } from 'react';
import { Type, Pencil } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { ConfirmDialog } from '~/components/confirm-dialog';
import { Badge } from '@kit/ui/badge';
import { defaultFonts } from '~/constants/brand.constants';
// import { defaultFonts, systemFonts } from '~/constants/brand.constants';
import { BrandFont, UploadedFont } from '~/types/brand';
// import { useFontLoader } from '~/hooks/use-font-loader';

import FontEdit from './FontEdit';
import UploadedFonts from './UploadedFonts';

interface BrandFontsProps {
  fonts: BrandFont[] | null;
  uploadedFonts: UploadedFont[];
  onSave: (fonts: BrandFont[]) => void;
}

// Font style descriptions for better UX
const fontStyleDescriptions: Record<string, string> = {
  title: 'The largest, most prominent text used for main headlines',
  subtitle: 'Secondary text that supports the title',
  heading: 'Section titles used to organize content',
  subheading: 'Secondary headings that support the main heading',
  section_header: 'Headings for smaller content sections',
  body: 'The main text content for paragraphs',
  quote: 'Text style for quotations or testimonials',
  caption: 'Small text used for image captions or footnotes',
};

const BrandFonts: React.FC<BrandFontsProps> = ({
  fonts,
  onSave,
  uploadedFonts: defaultUploadedFonts,
}) => {
  // Initialize with the default heading font - we know this exists in defaultFonts
  const defaultHeadingFont: BrandFont = {
    title: 'Heading',
    size: 20,
    fontFamily: 'Arial',
    isBold: true,
    isItalic: false,
    type: 'heading',
  };
  
  const [fontTitles, setFontTitles] = useState<BrandFont[]>([defaultHeadingFont]);
  const [selectedFont, setSelectedFont] = useState<BrandFont | null>(null);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadedFonts, setUploadedFonts] =
    useState<UploadedFont[]>(defaultUploadedFonts);
    
  // Use the font loader hook to load custom fonts
  // const { loadedFonts } = useFontLoader({
  //   uploadedFonts,
  //   fontTitles,
  //   systemFonts,
  // });

  useEffect(() => {
    if (!fonts || fonts.length === 0) {
      // Ensure there's always at least a heading font
      setFontTitles([defaultHeadingFont]);
      // Notify parent of the default font
      onSave([defaultHeadingFont]);
    } else {
      // Check if fonts is actually an array
      if (Array.isArray(fonts)) {
        setFontTitles(fonts);
      } else {
        // If fonts is not an array but has some value, try to parse it
        try {
          const parsedFonts = typeof fonts === 'string' ? JSON.parse(fonts) : [defaultHeadingFont];
          setFontTitles(Array.isArray(parsedFonts) ? parsedFonts : [defaultHeadingFont]);
        } catch (e) {
          console.error('Error parsing fonts:', e);
          setFontTitles([defaultHeadingFont]);
        }
      }
    }
  }, [fonts, onSave, defaultHeadingFont]);

  const handleSave = (font: BrandFont) => {
    const selectedFontIndex = fontTitles.findIndex(
      (fontTitle) => fontTitle.type === font?.type,
    );
    
    if (selectedFontIndex === -1) {
      // Add new font if it doesn't exist
      setFontTitles([...fontTitles, font]);
      onSave([...fontTitles, font]);
    } else {
      // Update existing font
      const newFontTitles = [...fontTitles];
      newFontTitles[selectedFontIndex] = font;
      setFontTitles(newFontTitles);
      onSave(newFontTitles);
    }
    
    setSelectedFont(null);
  };

  const handleCancel = () => {
    setSelectedFont(null);
  };

  const handleEdit = (fontType: string) => {
    const fontToEdit = fontTitles.find(font => font.type === fontType);
    if (fontToEdit) {
      setSelectedFont(fontToEdit);
    } else {
      // If not found in current fonts, use default
      const defaultFont = defaultFonts.find(font => font.type === fontType);
      if (defaultFont) {
        setSelectedFont({...defaultFont});
      }
    }
  };

  // const handleDelete = (fontType: string) => {
  //   const fontToDelete = fontTitles.find(font => font.type === fontType);
  //   if (fontToDelete) {
  //     setSelectedFont(fontToDelete);
  //     setIsConfirmOpen(true);
  //   }
  // };

  const confirmDelete = () => {
    if (selectedFont) {
      // Reset the font to default values
      const defaultFont = defaultFonts.find(
        (df) => df.type === selectedFont.type,
      ) as BrandFont;
      
      handleSave(defaultFont);
      setSelectedFont(null);
      setIsConfirmOpen(false);
    }
  };

  // Check if a font is custom (different from default)
  const isCustomFont = (fontType: string) => {
    const userFont = fontTitles.find(font => font.type === fontType);
    const defaultFont = defaultFonts.find(font => font.type === fontType);
    
    if (!userFont || !defaultFont) return false;
    
    return (
      userFont.fontFamily !== defaultFont.fontFamily ||
      userFont.size !== defaultFont.size ||
      userFont.isBold !== defaultFont.isBold ||
      userFont.isItalic !== defaultFont.isItalic
    );
  };

  // Get font styles as buttons
  const renderFontStyleButtons = () => {
    return defaultFonts.map(defaultFont => {
      const fontType = defaultFont.type;
      const isCustom = isCustomFont(fontType);
      const userFont = fontTitles.find(font => font.type === fontType) || defaultFont;
      
      return (
        <Card key={fontType} className="overflow-hidden">
          <CardHeader className="pb-0">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium">
                {defaultFont.title}
                {isCustom && <Badge className="ml-2" variant="outline">Customized</Badge>}
              </CardTitle>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleEdit(fontType)}
                className="h-7 w-7 p-0"
              >
                <Pencil className="h-3.5 w-3.5" />
              </Button>
            </div>
            <CardDescription className="text-xs">
              {fontStyleDescriptions[fontType] || 'Typography style'}
            </CardDescription>
          </CardHeader>
          <CardContent 
            className="pt-2 pb-4 cursor-pointer"
            onClick={() => handleEdit(fontType)}
          >
            <div className="flex flex-col items-center">
              <div 
                className="w-full text-center py-3 px-2 border rounded-md"
                style={{
                  fontFamily: userFont.fontFamily,
                  fontSize: `${userFont.size}px`,
                  fontWeight: userFont.isBold ? 'bold' : 'normal',
                  fontStyle: userFont.isItalic ? 'italic' : 'normal',
                }}
              >
                {userFont.title}
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                {userFont.fontFamily}, {userFont.size}px
                {userFont.isBold ? ', Bold' : ''}
                {userFont.isItalic ? ', Italic' : ''}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    });
  };

  return (
    <div className="space-y-6">
      {/* Font management options */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Typography Styles</CardTitle>
          <CardDescription>
            Define your brand&apos;s typography styles to maintain consistency
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Click on any style to customize it for your brand.
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Type className="h-4 w-4" />
              Manage Fonts ({uploadedFonts.length})
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Font styles grid */}
      {selectedFont ? (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Edit {selectedFont.title} Style</CardTitle>
          </CardHeader>
          <CardContent>
            <FontEdit
              defaultFont={selectedFont}
              handleSave={handleSave}
              handleCancel={handleCancel}
              uploadedFonts={uploadedFonts}
            />
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {renderFontStyleButtons()}
        </div>
      )}
      
      {/* Confirmation dialog for deleting custom styles */}
      <ConfirmDialog
        isOpen={isConfirmOpen}
        onOpenChange={setIsConfirmOpen}
        title="Reset Font Style"
        description={`Are you sure you want to reset ${selectedFont?.title} to its default style?`}
        confirmText="Reset"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        variant="danger"
      />
      
      {/* Font upload management dialog */}
      <UploadedFonts
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        uploadedFonts={uploadedFonts}
        setUploadedFonts={setUploadedFonts}
      />
    </div>
  );
};

export default BrandFonts;
