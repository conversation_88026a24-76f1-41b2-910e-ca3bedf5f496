import { Trash } from 'lucide-react';

import { defaultFonts } from '~/constants/brand.constants';
import { BrandFont } from '~/types/brand';

interface FontDisplayProps {
  font: BrandFont;
  handleClick: () => void;
  handleDelete: () => void;
}

export default function FontDisplay({
  font,
  handleClick,
  handleDelete,
}: FontDisplayProps) {
  const { title, size, fontFamily, isBold, isItalic, type } = font;

  const isFontDefault = () => {
    const defaultFont = defaultFonts.find(
      (defaultFont) => defaultFont.type === type,
    );

    return (
      defaultFont &&
      title === defaultFont.title &&
      size === defaultFont.size &&
      fontFamily === defaultFont.fontFamily &&
      isBold === defaultFont.isBold &&
      isItalic === defaultFont.isItalic
    );
  };

  return (
    <div
      className="flex cursor-pointer items-center justify-between p-4"
      onClick={() => handleClick()}
    >
      <span
        style={{
          fontSize: `${size}px`,
          fontFamily,
          fontWeight: isBold ? 'bold' : 'normal',
          fontStyle: isItalic ? 'italic' : 'normal',
        }}
      >
        {title}
      </span>
      <button
        onClick={(e) => {
          e.stopPropagation();
          handleDelete();
        }}
        className={`${isFontDefault() ? 'text-red-100' : 'text-red-500'} cursor-pointer bg-transparent`}
      >
        <Trash className="h-4 w-4" />
      </button>
    </div>
  );
}
