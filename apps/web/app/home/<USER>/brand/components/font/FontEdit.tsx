import { useRef, useState } from 'react';

import { Check, X } from 'lucide-react';

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';

import { fontSizes, systemFonts } from '~/constants/brand.constants';
import { BrandFont, UploadedFont } from '~/types/brand';
// import { useFontLoader } from '~/hooks/use-font-loader';

interface FontEditProps {
  defaultFont: BrandFont;
  handleSave: (font: BrandFont) => void;
  handleCancel: () => void;
  uploadedFonts: UploadedFont[];
}

export default function FontEdit({
  defaultFont,
  handleSave,
  handleCancel,
  uploadedFonts,
}: FontEditProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [font, setFont] = useState<BrandFont>(defaultFont);

  // Use the font loader hook to ensure fonts are properly loaded
  // const { loadedFonts } = useFontLoader({
  //   uploadedFonts,
  //   fontTitles: [font], // Pass current font to ensure it's loaded
  //   systemFonts,
  // });

  if (!font) return null;

  // Combine system fonts with uploaded fonts into a single array
  const allFonts = [
    ...systemFonts,
    ...uploadedFonts
      .map((f) => f.fontFamily)
      .filter((f) => !systemFonts.includes(f)),
  ];

  return (
    <div>
      <div className="flex space-x-2 p-2 shadow-xs shadow-gray-300">
        <Select
          onValueChange={(value) => setFont({ ...font, fontFamily: value })}
          value={font?.fontFamily}
        >
          <SelectTrigger className="w-auto">
            <SelectValue placeholder="Select a font" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Fonts</SelectLabel>
              {allFonts.map((fontName) => (
                <SelectItem key={fontName} value={fontName}>
                  {fontName}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>

        <Select
          onValueChange={(value) => setFont({ ...font, size: parseInt(value) })}
          value={font?.size?.toString()}
        >
          <SelectTrigger className="w-24">
            <SelectValue placeholder="Size" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Font Size</SelectLabel>
              {fontSizes.map((size) => (
                <SelectItem key={size} value={size}>
                  {size}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>

        <button
          onClick={() => setFont({ ...font, isBold: !font.isBold })}
          disabled={!font?.fontFamily}
          className={`flex h-8 w-8 items-center justify-center rounded ${font?.isBold ? 'bg-gray-200' : ''}`}
        >
          B
        </button>
        <button
          onClick={() => setFont({ ...font, isItalic: !font.isItalic })}
          disabled={!font?.fontFamily}
          className={`flex h-8 w-8 items-center justify-center rounded ${font?.isItalic ? 'bg-gray-200' : ''}`}
        >
          I
        </button>
      </div>
      <div className="relative w-full">
        <input
          ref={inputRef}
          className={`h-16 w-full border-b border-dotted p-2 outline-none ${font.title === 'Quote' ? 'border-l-4 pl-4 italic' : ''}`}
          placeholder="Paste font here"
          defaultValue={font.title}
          maxLength={50}
          style={{
            fontFamily: font.fontFamily,
            fontSize: `${font.size}px`,
            fontWeight: font?.isBold ? 'bold' : 'normal',
            fontStyle: font.isItalic ? 'italic' : 'normal',
          }}
          onChange={(e) =>
            setFont({
              ...font,
              title: e.target.value,
            })
          }
        />
        <div className="absolute top-2 right-2 flex space-x-2">
          <button
            onClick={() => handleSave(font)}
            className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-gray-200"
            title="Save"
          >
            <Check className="h-4 w-4" />
          </button>
          <button
            onClick={handleCancel}
            className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-gray-200"
            title="Cancel"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
