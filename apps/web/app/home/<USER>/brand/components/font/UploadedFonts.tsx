import React, { useState } from 'react';

import { Trash } from 'lucide-react';
import { useDropzone } from 'react-dropzone';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { LoadingOverlay } from '@kit/ui/loading-overlay';

import { useFontLoader } from '~/hooks/use-font-loader';
import { deleteBrandFont, uploadNewBrandFont } from '~/services/brand';
import { fetchFonts } from '~/services/storage';
import { UploadedFont } from '~/types/brand';
import { systemFonts } from '~/constants/brand.constants';

interface UploadedFontsProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  uploadedFonts: UploadedFont[];
  setUploadedFonts: (uploadedFonts: UploadedFont[]) => void;
}

const UploadedFonts: React.FC<UploadedFontsProps> = ({
  isOpen,
  onOpenChange,
  uploadedFonts,
  setUploadedFonts,
}) => {
  const { account } = useTeamAccountWorkspace();
  const [loading, setLoading] = useState(false);
  const [busy, setBusy] = useState(false);

  const { loadedFonts } = useFontLoader({
    uploadedFonts,
    systemFonts,
  });

  const fetchAndSetFonts = async () => {
    setLoading(true);
    const fonts = (await fetchFonts(account.id)) as UploadedFont[];
    setUploadedFonts(fonts);
    setLoading(false);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'font/ttf': ['.ttf'],
      'font/otf': ['.otf'],
      'font/woff': ['.woff'],
      'font/woff2': ['.woff2'],
    },
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        setBusy(true);
        await uploadNewBrandFont(account.id, acceptedFiles[0] as File);
        setBusy(false);
        fetchAndSetFonts();
      }
    },
  });

  const handleDelete = async (font: UploadedFont) => {
    deleteBrandFont(account.id, font.name);
    fetchAndSetFonts();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Uploaded Fonts</DialogTitle>
        </DialogHeader>
        <div className="mt-2 space-y-4">
          {loading || busy ? (
            <LoadingOverlay fullPage={false}>
              {loading ? 'Loading fonts...' : 'Uploading font...'}
            </LoadingOverlay>
          ) : (
            <div className="divide-y divide-gray-200">
              {uploadedFonts?.map((font) => (
                <div key={font.name} className="py-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{font.fontFamily}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(font);
                      }}
                      className={`cursor-pointer bg-transparent text-red-500`}
                    >
                      <Trash className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div 
                    className="mt-2 text-sm p-2 border rounded" 
                    style={{ fontFamily: font.fontFamily }}
                  >
                    The quick brown fox jumps over the lazy dog
                  </div>
                  <div className="mt-1 text-xs text-gray-500">
                    {loadedFonts[font.fontFamily] ? 
                      "✓ Font loaded successfully" : 
                      "⚠️ Font not loaded"}
                  </div>
                </div>
              ))}
            </div>
          )}
          <div
            {...getRootProps()}
            className={`mt-10 flex w-full cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dotted border-gray-300 p-4 transition-colors hover:bg-gray-100 ${isDragActive ? 'bg-gray-200' : ''}`}
          >
            <input {...getInputProps()} />
            <span className="mt-2 text-center text-sm text-gray-500">
              Drag and drop a font file here, or click to select a file
            </span>
          </div>
        </div>
        <DialogFooter className="mt-4 flex flex-row justify-end gap-2 sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UploadedFonts;
