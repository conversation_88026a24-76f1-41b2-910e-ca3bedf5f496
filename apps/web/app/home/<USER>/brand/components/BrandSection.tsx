import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@kit/ui/accordion';

import './_styles/brand-section.css';

interface BrandSectionProps {
  title: string;
  children: React.ReactNode;
  value: string;
  styleClass?: string;
  id: string; // Added id property
}

function BrandSection({
  title,
  children,
  value,
  styleClass,
  id, // Added id parameter
}: BrandSectionProps) {
  return (
    <AccordionItem
      value={value}
      className={`accordion-item ${styleClass || ''}`}
      id={id} // Applied id to AccordionItem
    >
      <AccordionTrigger 
        className="cursor-pointer text-2xl font-bold hover:no-underline"
        onClick={() => {
          // Add a small delay to let the accordion animation start
          setTimeout(() => {
            const element = document.getElementById(id);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }, 100);
        }}
      >
        {title}
      </AccordionTrigger>
      <AccordionContent className="overflow-ellipsis">
        {children}
      </AccordionContent>
    </AccordionItem>
  );
}

export default BrandSection;
