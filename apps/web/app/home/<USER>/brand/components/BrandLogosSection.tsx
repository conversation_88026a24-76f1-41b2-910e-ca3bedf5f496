'use client';

import React from 'react';
import BrandSection from './BrandSection';
import BrandLogos from './logo/BrandLogos';
import { useBrand } from './BrandContext';
import ErrorBoundary from './ErrorBoundary';

export default function BrandLogosSection() {
  const { logos, addLogo, deleteLogo } = useBrand();
  
  return (
    <ErrorBoundary>
      <BrandSection
        title={`Logos (${logos.length})`}
        value="brand_logos"
        id="logos-section"
      >
        <BrandLogos
          logos={logos}
          onAdd={addLogo}
          onDelete={deleteLogo}
        />
      </BrandSection>
    </ErrorBoundary>
  );
}
