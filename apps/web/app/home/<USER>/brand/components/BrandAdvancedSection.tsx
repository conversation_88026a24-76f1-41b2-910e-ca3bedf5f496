'use client';

import React from 'react';
import BrandSection from './BrandSection';
import DangerZone from './DangerZone';
import { useBrand } from './BrandContext';
import ErrorBoundary from './ErrorBoundary';

export default function BrandAdvancedSection() {
  const { setIsDeleteDialogOpen } = useBrand();
  
  return (
    <ErrorBoundary>
      <BrandSection
        title="Advanced"
        value="brand_advanced"
        id="advanced-section"
      >
        <DangerZone onDelete={() => setIsDeleteDialogOpen(true)} />
      </BrandSection>
    </ErrorBoundary>
  );
}
