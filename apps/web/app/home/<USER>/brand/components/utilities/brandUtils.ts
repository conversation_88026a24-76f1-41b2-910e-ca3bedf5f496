import { BrandColorPalette, BrandFont } from '~/types/brand';

/**
 * NOTE FOR DEVELOPERS:
 * The brand_colors and brand_fonts fields in the database are JSONB types and
 * should be handled as direct JSON objects. Supabase automatically converts these
 * to/from JSON. Do NOT manually stringify these values before sending to the API.
 */

/**
 * Ensures brand colors are always returned as a properly formatted array
 */
export function normalizeBrandColors(colors: any): BrandColorPalette[] {
  if (!colors) return [];
  
  // Handle array data directly
  if (Array.isArray(colors)) {
    return colors;
  }
  
  // Fall back to empty array for any unexpected format
  console.warn('Unexpected format for brand colors:', colors);
  return [];
}

/**
 * Ensures brand fonts are always returned in the correct format
 */
export function normalizeBrandFonts(fonts: any): BrandFont[] | null {
  if (!fonts) return null;
  
  // Handle array data directly
  if (Array.isArray(fonts)) {
    return fonts;
  }
  
  // Fall back to null for any unexpected format
  console.warn('Unexpected format for brand fonts:', fonts);
  return null;
}

export function getTotalColorsCount(palettes: BrandColorPalette[]) {
  if (!palettes) return 0;
  return palettes.reduce(
    (total, palette) => total + (palette.colors?.length || 0),
    0,
  );
}
