interface DangerZoneProps {
  onDelete: () => void;
}

export default function DangerZone({ onDelete }: DangerZoneProps) {
  return (
    <div className="flex flex-col items-center">
      <h2 className="mb-2 text-xl font-semibold text-red-600">Danger Zone</h2>
      <p className="mb-4 text-center text-gray-600">
        Delete your brand to start fresh. This will permanently remove all brand details,
        logos, fonts, and color palettes. This action cannot be undone.
      </p>
      <button
        onClick={onDelete}
        className="rounded-md bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
      >
        Delete Entire Brand
      </button>
    </div>
  );
}
