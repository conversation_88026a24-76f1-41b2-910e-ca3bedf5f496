'use client';

import { createContext, useContext, useState, ReactNode, useRef, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  Brand,
  BrandColorPalette,
  BrandFont,
  UploadedFont,
} from '~/types/brand';
import {
  createCompanyBrand,
  deleteBrand,
  deleteBrandLogo,
  updateBrandDetails,
  uploadNewBrandLogo,
} from '~/services/brand';
import { normalizeBrandColors, normalizeBrandFonts } from './utilities/brandUtils';

interface BrandContextType {
  brand: Brand & { id: string };
  logos: Array<{ name: string; url: string }>;
  uploadedFonts: Array<UploadedFont>;
  updateBrandSection: (key: string, value: any) => void;
  addLogo: (files: File[]) => Promise<void>;
  deleteLogo: (fileName: string) => Promise<void>;
  getBrandColors: () => BrandColorPalette[];
  getBrandFonts: () => BrandFont[] | null;
  refetchBrand: () => void;
  isDeleteDialogOpen: boolean;
  setIsDeleteDialogOpen: (isOpen: boolean) => void;
  isDeletePaletteDialogOpen: boolean;
  setIsDeletePaletteDialogOpen: (isOpen: boolean) => void;
  paletteToDelete: number | null;
  setPaletteToDelete: (index: number | null) => void;
  handleDeleteBrand: () => Promise<void>;
  confirmDeletePalette: () => void;
  handleDeletePalette: (paletteIndex: number) => void;
  isNewBrand: boolean;
  createBrand: () => Promise<void>;
}

const BrandContext = createContext<BrandContextType | undefined>(undefined);

export const BrandProvider = ({
  children,
  initialBrand,
  initialLogos,
  initialUploadedFonts,
  refetchBrand,
  isNewBrand = false,
}: {
  children: ReactNode;
  initialBrand: Brand & { id: string };
  initialLogos: Array<{ name: string; url: string }>;
  initialUploadedFonts: Array<UploadedFont>;
  refetchBrand: () => void;
  isNewBrand?: boolean;
}) => {
  const queryClient = useQueryClient();
  
  const [logos, setLogos] = useState(initialLogos);
  const [brand, setBrand] = useState(initialBrand);
  const [uploadedFonts] = useState(initialUploadedFonts);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeletePaletteDialogOpen, setIsDeletePaletteDialogOpen] = useState(false);
  const [paletteToDelete, setPaletteToDelete] = useState<number | null>(null);
  
  // Track the last time we showed a toast
  const lastToastTimeRef = useRef<number>(0);
  // Minimum time between toasts in milliseconds (3 seconds)
  const TOAST_DEBOUNCE_MS = 3000;
  
  // Debounce API calls
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingUpdatesRef = useRef<Record<string, any>>({});
  const API_DEBOUNCE_MS = 1000; // 1 second delay for API calls

  const updateBrandMutation = useMutation({
    mutationFn: async (updates: any) => {
      return await updateBrandDetails(brand.company_id, brand?.id, updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['brand', brand.company_id] });
      
      // Only show toast if enough time has passed since the last toast
      const now = Date.now();
      if (now - lastToastTimeRef.current > TOAST_DEBOUNCE_MS) {
        toast.success('Brand updated successfully');
        lastToastTimeRef.current = now;
      }
    },
    onError: (error) => {
      toast.error('Failed to update brand');
      console.error('Error updating brand section', error);
      // Rollback on error
      setBrand(initialBrand);
    },
  });

  const deleteBrandMutation = useMutation({
    mutationFn: async () => {
      return await deleteBrand(brand.company_id, brand.id);
    },
    onSuccess: () => {
      // Don't show toast here as we'll handle it in handleDeleteBrand
      refetchBrand();
    },
    onError: (error) => {
      // Don't show toast here as we'll handle it in handleDeleteBrand
      console.error('Error deleting brand:', error);
    },
  });

  const getBrandColors = (): BrandColorPalette[] => {
    return normalizeBrandColors(brand?.brand_colors);
  };

  const getBrandFonts = (): BrandFont[] | null => {
    return normalizeBrandFonts(brand?.brand_fonts);
  };

  const createBrandMutation = useMutation({
    mutationFn: async () => {
      // Only create if this is a new brand without an ID
      if (!brand.id) {
        return await createCompanyBrand({
          ...brand,
          company_id: brand.company_id,
        });
      }
      return null;
    },
    onSuccess: (data) => {
      if (data) {
        toast.success('Brand created successfully');
        // Update local state with the created brand ID
        setBrand((current) => ({
          ...current,
          id: data.id,
        }));
        queryClient.invalidateQueries({ queryKey: ['brand', brand.company_id] });
      }
    },
    onError: (error) => {
      toast.error('Failed to create brand');
      console.error('Error creating brand:', error);
    },
  });

  // Send pending updates to the server
  const flushPendingUpdates = useCallback(() => {
    const updates = pendingUpdatesRef.current;
    
    // If we have pending updates, send them to the server
    if (Object.keys(updates).length > 0) {
      updateBrandMutation.mutate(updates);
      // Clear pending updates
      pendingUpdatesRef.current = {};
    }
    
    // Clear the timeout
    updateTimeoutRef.current = null;
  }, [updateBrandMutation]);

  const updateBrandSection = (
    key: string,
    value: string | string[] | BrandColorPalette[] | BrandFont[],
  ) => {
    // Optimistically update the local state
    setBrand((current) => ({
      ...current,
      [key]: value,
    }));

    // If this is a new brand, don't try to update on the server yet
    // (we'll create the brand when the user explicitly saves)
    if (!isNewBrand && brand.id) {
      // Store the update in the pending updates
      pendingUpdatesRef.current[key] = value;
      
      // Clear existing timeout if it exists
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      
      // Set a new timeout to send updates after delay
      updateTimeoutRef.current = setTimeout(flushPendingUpdates, API_DEBOUNCE_MS);
    }
  };

  const createBrand = async () => {
    if (isNewBrand) {
      await createBrandMutation.mutateAsync();
      refetchBrand();
    }
  };

  const handleDeleteBrand = async () => {
    try {
      setIsDeleteDialogOpen(false);
      toast.loading('Deleting brand and all associated assets...');
      await deleteBrandMutation.mutateAsync();
      toast.success('Brand completely deleted. You can now start fresh.');
    } catch (error) {
      console.error('Error deleting brand:', error);
      toast.error('Failed to delete brand and its assets');
    }
  };

  const createLogoObject = (file: File) => {
    const fileName = file.name;
    const url = URL.createObjectURL(file);
    return {
      name: fileName,
      url,
    };
  };

  const addLogo = async (files: File[]) => {
    try {
      // Create temporary logo objects with local URLs
      const tempLogos = files.map(file => createLogoObject(file));
      
      // Optimistically add the logos to state
      setLogos((current) => [...current, ...tempLogos]);
      
      // Process each file sequentially
      const uploadedLogos = [];
      for (const file of files) {
        const newLogo = await uploadNewBrandLogo(brand.company_id, file);
        uploadedLogos.push(newLogo);
      }
      
      // Update all logos in state with real URLs
      setLogos((current) => {
        // Filter out temporary logos and add permanent ones
        const withoutTempLogos = current.filter(l => 
          !files.some(file => file.name === l.name) || 
          uploadedLogos.some(uploaded => uploaded.name === l.name)
        );
        return [...withoutTempLogos, ...uploadedLogos.filter(ul => 
          !withoutTempLogos.some(l => l.name === ul.name)
        )];
      });
      
      // Cleanup temporary URLs
      tempLogos.forEach(logo => URL.revokeObjectURL(logo.url));
      
      // Refresh the server state as backup
      queryClient.invalidateQueries({ queryKey: ['brand', brand.company_id] });
      
      const message = files.length > 1 ? 'Logos uploaded successfully' : 'Logo uploaded successfully';
      toast.success(message);
    } catch (error) {
      // Rollback on error
      console.error('Error uploading logos:', error);
      setLogos(initialLogos);
      toast.error('Failed to upload logos');
    }
  };

  const deleteLogo = async (fileName: string) => {
    try {
      // Optimistically remove the logo from the local state
      setLogos((currentLogos) =>
        currentLogos.filter((logo) => logo.name !== fileName),
      );

      // Actually perform the deletion
      await deleteBrandLogo(brand.company_id, fileName);

      // Refresh the server state as backup
      queryClient.invalidateQueries({ queryKey: ['brand', brand.company_id] });
      toast.success('Logo deleted successfully');
    } catch (error) {
      // Rollback on error
      console.error('Error deleting logo:', error);
      setLogos(initialLogos);
      toast.error('Failed to delete logo');
    }
  };

  const handleDeletePalette = (paletteIndex: number) => {
    setPaletteToDelete(paletteIndex);
    setIsDeletePaletteDialogOpen(true);
  };

  const confirmDeletePalette = () => {
    if (paletteToDelete === null) return;

    // Remove the palette from the local state
    const newPalettes = getBrandColors().filter(
      (_, i) => i !== paletteToDelete,
    );
    updateBrandSection('brand_colors', newPalettes);
    setPaletteToDelete(null);
    setIsDeletePaletteDialogOpen(false);
  };

  return (
    <BrandContext.Provider
      value={{
        brand,
        logos,
        uploadedFonts,
        updateBrandSection,
        addLogo,
        deleteLogo,
        getBrandColors,
        getBrandFonts,
        refetchBrand,
        isDeleteDialogOpen,
        setIsDeleteDialogOpen,
        isDeletePaletteDialogOpen,
        setIsDeletePaletteDialogOpen,
        paletteToDelete,
        setPaletteToDelete,
        handleDeleteBrand,
        confirmDeletePalette,
        handleDeletePalette,
        isNewBrand,
        createBrand,
      }}
    >
      {children}
    </BrandContext.Provider>
  );
};

export const useBrand = () => {
  const context = useContext(BrandContext);
  if (context === undefined) {
    throw new Error('useBrand must be used within a BrandProvider');
  }
  return context;
};
