'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useEffect } from 'react';

import { getBrandData } from '~/services/brand';
import { Brand } from '~/types/brand';

import { BrandDisplay } from './BrandDisplay';
import BrandLoading from './BrandLoading';
import BrandError from './BrandError';
import ErrorBoundary from './ErrorBoundary';

export const BrandContent = () => {
  const workspace = useTeamAccountWorkspace();
  const queryClient = useQueryClient();
  
  // Force refresh the brand data when the account changes
  useEffect(() => {
    // Clear cache for this account's brand data
    queryClient.invalidateQueries({ queryKey: ['brand', workspace.account.id] });
  }, [workspace.account.id, queryClient]);
  
  const {
    data: brandData,
    isFetching: fetching,
    isRefetching: refetching,
    refetch: refetchBrand,
    error,
  } = useQuery({
    queryKey: ['brand', workspace.account.id],
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
    queryFn: async () => {
      try {
        const result = await getBrandData(workspace.account.id);
        
        // If there's no brand, create an empty default structure
        if(!result?.brand) {
          return {
            brand: {
              id: '',
              company_id: workspace.account.id,
              brand_colors: [],
              brand_fonts: [],
              mission: '',
              vision: '',
              voice: '',
              value_proposition: '',
              audience: '',
              messaging_pillars: '',
              identity: '',
              guidelines: '',
              personality: '',
              product_list: '',
            },
            logos: [],
            fonts: [],
          };
        }
        
        return {
          brand: result?.brand as Brand,
          logos: result?.logos || [],
          fonts: result?.fonts || [],
        };
      } catch (error) {
        console.error('Error fetching brand data:', error);
        throw error;
      }
    },
  });

  // Show loading state
  if (fetching && !refetching) {
    return <BrandLoading />;
  }

  // Show error state for actual errors
  if (error) {
    return <BrandError error={error as Error} />;
  }

  // Always show brand display, even for empty brand
  return (
    <ErrorBoundary>
      <div className="h-full">
        <BrandDisplay
          brand={brandData?.brand as Brand & { id: string }}
          logos={brandData?.logos || []}
          uploadedFonts={brandData?.fonts || []}
          refetchBrand={refetchBrand}
          isNewBrand={!brandData?.brand?.id}
        />
      </div>
    </ErrorBoundary>
  );
};
