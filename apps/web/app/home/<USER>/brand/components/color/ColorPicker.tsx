import React, { useState } from 'react';

import { Plus } from 'lucide-react';
import { SketchPicker } from 'react-color';

interface ColorPickerProps {
  onAddColor: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ onAddColor }) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);

  return (
    <div className="relative w-max p-2">
      <div
        className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center border-2 border-dotted border-gray-300"
        onClick={() => setIsPickerOpen(true)}
      >
        <Plus className="h-8 w-8 text-gray-500" />
        <span className="mt-2 text-sm text-gray-500">Add Color</span>
      </div>
      {isPickerOpen && (
        <div className="absolute top-[100%] z-20">
          <div
            className="fixed inset-0"
            onClick={() => setIsPickerOpen(false)}
          />
          <SketchPicker
            color="#ffffff"
            onChangeComplete={(colorResult) => {
              onAddColor(colorResult.hex);
              setIsPickerOpen(false);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ColorPicker;
