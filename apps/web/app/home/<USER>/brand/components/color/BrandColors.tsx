import React from 'react';
import { Trash, Palette, Plus } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';

import EditableSection from '~/components/editable-section';
import { BrandColorInterface, BrandColorPalette } from '~/types/brand';

import BrandColor from './BrandColor';
import ColorPicker from './ColorPicker';

interface BrandColorsProps {
  colors: BrandColorPalette[];
  onSave: (colors: BrandColorPalette[]) => void;
  onDeletePalette: (paletteIndex: number) => void;
}

const BrandColors: React.FC<BrandColorsProps> = ({
  colors,
  onSave,
  onDeletePalette,
}) => {
  const removeColor = (paletteIndex: number, colorIndex: number) => {
    const newPalettes = colors.map((palette, i) =>
      i === paletteIndex
        ? {
            ...palette,
            colors: palette.colors.filter((_, j) => j !== colorIndex),
          }
        : palette,
    );
    onSave(newPalettes);
  };

  const changeColor = (
    paletteIndex: number,
    colorIndex: number,
    newColor: BrandColorInterface,
  ) => {
    const newPalettes = colors.map((palette, i) =>
      i === paletteIndex
        ? {
            ...palette,
            colors: palette.colors.map((color, j) =>
              j === colorIndex ? newColor : color,
            ),
          }
        : palette,
    );
    onSave(newPalettes);
  };

  const addColor = (paletteIndex: number, newColor: BrandColorInterface) => {
    const newPalettes = colors.map((palette, i) =>
      i === paletteIndex
        ? { ...palette, colors: [...palette.colors, newColor] }
        : palette,
    ) as BrandColorPalette[];
    onSave(newPalettes);
  };

  const addPalette = () => {
    const newPalettes = [...colors, { name: 'New Palette', colors: [] }];
    onSave(newPalettes);
  };

  const changePaletteName = (index: number, newName: string) => {
    const newPalettes = colors.map((palette, i) =>
      i === index ? { ...palette, name: newName } : palette,
    );
    onSave(newPalettes);
  };

  return (
    <div className="space-y-4">
      {colors.length === 0 && (
        <Card className="mb-6 border-dashed">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Add Your Brand Colors</CardTitle>
            <CardDescription>
              Define your brand&apos;s color palette to ensure consistency across all content
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <Palette className="mb-4 h-12 w-12 text-muted-foreground" />
              <p className="mb-4 text-muted-foreground">No color palettes defined yet</p>
              <div 
                onClick={addPalette}
                className="flex cursor-pointer items-center rounded-md border border-dashed border-muted-foreground px-4 py-2 text-sm font-medium text-muted-foreground hover:bg-secondary"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Color Palette
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {colors.map((palette, paletteIndex) => (
        <div
          key={`palette-${paletteIndex}`}
          className="mb-2 rounded-lg border border-gray-100"
        >
          <div className="flex justify-between bg-gray-50 p-2">
            <div className="w-full">
              <EditableSection
                title=""
                content={palette.name}
                onSave={(newName) => changePaletteName(paletteIndex, newName)}
                useTextarea={false}
              />
            </div>
            <button
              onClick={() => onDeletePalette(paletteIndex)}
              className="ml-2 text-red-500"
            >
              <Trash className="h-4 w-4" />
            </button>
          </div>
          <div className="flex flex-wrap gap-4 rounded p-2">
            {palette.colors?.map((color, colorIndex) => (
              <BrandColor
                key={`color-${paletteIndex}-${colorIndex}`}
                color={color}
                onRemove={() => removeColor(paletteIndex, colorIndex)}
                onChange={(newColor) =>
                  changeColor(paletteIndex, colorIndex, newColor)
                }
              />
            ))}
            <ColorPicker
              onAddColor={(newColor) =>
                addColor(paletteIndex, { name: newColor, hex: newColor })
              }
            />
          </div>
        </div>
      ))}
      
      {colors.length > 0 && (
        <div
          onClick={addPalette}
          className="mt-4 flex cursor-pointer items-center justify-center rounded border-2 border-dashed border-gray-400 p-4 text-gray-500 hover:bg-gray-50"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Color Palette
        </div>
      )}
    </div>
  );
};

export default BrandColors;
