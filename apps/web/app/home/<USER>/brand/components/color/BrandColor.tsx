import React, { useState } from 'react';

import { X } from 'lucide-react';
import { SketchPicker } from 'react-color';

import EditableSection from '~/components/editable-section';
import { BrandColorInterface } from '~/types/brand';

interface BrandColorProps {
  color: BrandColorInterface;
  onRemove: () => void;
  onChange: (color: BrandColorInterface) => void;
}

const BrandColor: React.FC<BrandColorProps> = ({
  color,
  onRemove,
  onChange,
}) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);

  // Check if a string looks like a hex color (with or without #)
  const isHexColor = (str: string) => {
    return /^#?([0-9A-F]{6}|[0-9A-F]{3})$/i.test(str);
  };

  const handleColorChange = (colorResult: any) => {
    // If the current name looks like a hex color, update it to match the new hex
    // Otherwise, keep the custom name that the user has set
    const newHex = colorResult.hex;
    let newName = color.name;
    
    if (isHexColor(color.name)) {
      newName = newHex;
    }
    
    onChange({ ...color, hex: newHex, name: newName });
  };

  const handleNameChange = (newName: string) => {
    onChange({ ...color, name: newName });
  };

  return (
    <div className="group/color relative flex w-max p-2">
      <div className="w-max cursor-pointer">
        <div className="rounded-lg border-1 border-white p-2 group-hover/color:border-gray-100">
          <div
            className="aspect-square h-24 w-24 cursor-pointer rounded-lg shadow-md"
            style={{ backgroundColor: color.hex }}
            onClick={() => setIsPickerOpen(true)}
          />
        </div>
        <div className="mt-2 text-center">
          <EditableSection
            title=""
            content={color.name}
            onSave={handleNameChange}
            useTextarea={false}
          />
        </div>
      </div>
      <button
        className="absolute -top-1 -right-1 hidden cursor-pointer group-hover/color:block"
        onClick={onRemove}
      >
        <X className="h-4 w-4 text-gray-500" />
      </button>
      {isPickerOpen && (
        <div className="absolute top-[100%] z-20">
          <div
            className="fixed inset-0"
            onClick={() => setIsPickerOpen(false)}
          />
          <SketchPicker color={color.hex} onChange={handleColorChange} />
        </div>
      )}
    </div>
  );
};

export default BrandColor;
