/* Brand section custom styles */

/* Override parent container to prevent nested scrolling */
.shadcn-page-content {
  overflow: hidden !important;
  height: 100% !important;
}

/* Main brand container */
.brand-main-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Sidebar */
.brand-sidebar-wrapper {
  width: 260px;
  min-width: 200px;
  border-right: 1px solid #e5e7eb;
  padding: 1.5rem 1rem;
  overflow-y: auto;
}

/* Content area */
.brand-content-area {
  flex-grow: 1;
  padding: 1.5rem;
  overflow-y: auto;
  height: calc(100vh - 64px); /* Adjust based on header height */
  position: relative;
}