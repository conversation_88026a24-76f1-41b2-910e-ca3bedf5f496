import React from 'react';

import Image from 'next/image';

import { X } from 'lucide-react';

interface BrandLogoProps {
  name: string;
  url: string;
  onDelete: (name: string) => void;
}

const BrandLogo: React.FC<BrandLogoProps> = ({ name, url, onDelete }) => (
  <div className="group/logo relative aspect-square rounded-xl bg-gray-50 transition-colors hover:bg-gray-100">
    <Image
      src={url}
      alt={name}
      fill
      className="object-contain p-2"
      loading="lazy"
    />
    <div className="absolute right-2 bottom-2 left-2 text-center">
      <span className="rounded-full bg-white/90 px-2 py-1 text-sm">{name}</span>
    </div>
    <button
      className="absolute top-2 right-2 z-10 hidden cursor-pointer group-hover/logo:block"
      onClick={() => onDelete(name)}
    >
      <X className="h-4 w-4" />
    </button>
  </div>
);

export default BrandLogo;
