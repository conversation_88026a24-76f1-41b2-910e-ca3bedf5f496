import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Info } from 'lucide-react';

import AddLogo from './AddLogo';
import BrandLogo from './BrandLogo';

interface BrandLogosProps {
  logos: Array<{ name: string; url: string }>;
  onAdd: (files: File[]) => Promise<void>;
  onDelete: (fileName: string) => Promise<void>;
}

const BrandLogos: React.FC<BrandLogosProps> = ({ logos, onAdd, onDelete }) => {
  return (
    <>
      <div className="space-y-4">
        {!logos.length && (
          <Card className="mb-6 border-dashed">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Add Your Brand Logos</CardTitle>
              <CardDescription>
                Upload your brand logos to use them in your content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center p-6">
                <AddLogo onAdd={onAdd} isEmpty={true} />
              </div>
              <div className="mt-2 flex items-center text-xs text-muted-foreground">
                <Info className="mr-1 h-3 w-3" />
                <span>Upload multiple logos at once. Recommended formats: PNG, SVG, or JPG with transparent background</span>
              </div>
            </CardContent>
          </Card>
        )}
        
        <div className="grid grid-cols-2 gap-6 md:grid-cols-4 lg:grid-cols-6">
          {logos.map((logo, index) => (
            <BrandLogo
              key={index}
              name={logo.name}
              url={logo.url}
              onDelete={onDelete}
            />
          ))}
          {logos.length > 0 && <AddLogo onAdd={onAdd} isEmpty={false} />}
        </div>
      </div>
    </>
  );
};

export default BrandLogos;
