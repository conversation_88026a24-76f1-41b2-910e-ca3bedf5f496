import React from 'react';

import { Plus } from 'lucide-react';
import { useDropzone } from 'react-dropzone';

interface AddLogoProps {
  onAdd: (files: File[]) => Promise<void>;
  isEmpty?: boolean;
}

const AddLogo: React.FC<AddLogoProps> = ({ onAdd }) => {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': [] },
    multiple: true,
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        await onAdd(acceptedFiles as File[]);
      }
    },
  });

  return (
    <div
      {...getRootProps()}
      className={`flex w-full cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dotted border-gray-300 p-4 transition-colors hover:bg-gray-100 ${isDragActive ? 'bg-gray-200' : ''}`}
    >
      <input {...getInputProps()} />
      <Plus className="h-8 w-8 text-gray-500" />
      <span className="mt-2 text-center text-sm text-gray-500">
        Add logos or drag and drop
      </span>
      <span className="mt-1 text-center text-xs text-muted-foreground">
        You can select multiple files
      </span>
    </div>
  );
};

export default AddLogo;
