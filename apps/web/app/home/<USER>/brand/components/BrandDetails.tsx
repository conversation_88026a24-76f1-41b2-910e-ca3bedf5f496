'use client'
import { useState, useRef } from 'react';
import { Plus, Globe, FileText, Loader2 } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { toast } from 'sonner';
import EditableSection from '~/components/editable-section';
import { Brand, BrandColorPalette } from '~/types/brand';
import { getLocalApi } from '~/utils/api.util';
import { useBrand } from './BrandContext';

// Vercel Blob import - you'll need to install @vercel/blob
// pnpm add @vercel/blob
import { upload } from '@vercel/blob/client';

interface BrandDetailsProps {
  brand: Brand;
  handleSectionUpdate: (section: string, content: string | BrandColorPalette[]) => void;
}

// Define all available brand detail fields
const BRAND_DETAILS = [
  { key: 'mission', title: 'Mission', description: 'Your company\'s core purpose and reason for existence' },
  { key: 'vision', title: 'Vision', description: 'What your brand aims to achieve in the future' },
  { key: 'value_proposition', title: 'Value Proposition', description: 'The unique value your brand offers to customers' },
  { key: 'audience', title: 'Target Audience', description: 'The specific customers your brand serves' },
  { key: 'personality', title: 'Brand Personality', description: 'The human characteristics associated with your brand' },
  { key: 'voice', title: 'Brand Voice', description: 'How your brand communicates with customers' },
  { key: 'identity', title: 'Brand Identity', description: 'The visual and verbal elements that represent your brand' },
  { key: 'messaging_pillars', title: 'Messaging Pillars', description: 'The core themes that drive your brand\'s messaging' },
  { key: 'guidelines', title: 'Brand Guidelines', description: 'Rules for consistent brand representation' }
];

// Function to check if a URL is valid
const isValidUrl = (url: string) => {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

// Function to extract brand info from text
async function extractBrandInfo(text: string) {
  try {
    const response = await getLocalApi().post('/ai/extract-brand-info-from-text', {
      brand_text: JSON.stringify(text),
      brand_name: 'Company',
    });
    
    if (!response.status || response.status !== 200) {
      throw new Error('Failed to extract brand information');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error extracting brand information:', error);
    throw error;
  }
}

export default function BrandDetails({
  brand,
  handleSectionUpdate,
}: BrandDetailsProps) {
  const { isNewBrand, createBrand } = useBrand();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUrlModalOpen, setIsUrlModalOpen] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [urlError, setUrlError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Track which optional fields are active
  const [activeFields, setActiveFields] = useState<string[]>(
    // Initialize with fields that have content
    BRAND_DETAILS
      .filter(field => !!brand[field.key as keyof Brand])
      .map(field => field.key)
  );

  // Add a new field
  const addField = (key: string) => {
    setActiveFields([...activeFields, key]);
    // Initialize with empty string
    handleSectionUpdate(key, '');
  };

  // Activate all fields
  const activateAllFields = () => {
    setActiveFields(BRAND_DETAILS.map(field => field.key));
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      toast.error('Please upload a PDF file');
      return;
    }

    setIsLoading(true);
    
    try {
      // Reset the file input to allow the same file to be uploaded again
      event.target.value = '';
      
      // Upload file to Vercel Blob
      const blob = await upload(file.name, file, {
        access: 'public',
        handleUploadUrl: '/api/documents/upload',
      });

      // Process the uploaded file using the blob URL
      const response = await fetch('/api/documents/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ blobUrl: blob.url }),
      });

      if (!response.ok) {
        throw new Error('Failed to extract text from file');
      }

      const extractedText = await response.json();
      
      // Process the extracted text with AI
      const brandInfo = await extractBrandInfo(extractedText);
      
      // Activate all fields before updating
      activateAllFields();
      
      // Update each field with extracted info
      Object.entries(brandInfo).forEach(([key, value]) => {
        // Special handling for brand_colors
        if (key === 'brand_colors' && Array.isArray(value) && value.length > 0) {
          // Transform the simple array of color hex values to BrandColorPalette format
          const formattedColors = [{
            name: 'Primary Colors',
            colors: (value as string[]).map(hex => ({
              name: hex,
              hex: hex.startsWith('#') ? hex : `#${hex}`
            }))
          }];
          
          handleSectionUpdate(key, formattedColors);
        } 
        // For other fields, handle normally
        else if (BRAND_DETAILS.some(field => field.key === key)) {
          handleSectionUpdate(key, value as string);
        }
      });
      
      // Auto-save if this is a new brand
      if (isNewBrand) {
        await createBrand();
      }
      
      toast.success('Brand information extracted successfully');
    } catch (error) {
      console.error('Error processing document:', error);
      toast.error('Failed to extract brand information from document');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle URL change in the modal
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setWebsiteUrl(url);

    if (!url) {
      setUrlError('');
    } else if (!url.startsWith('https://')) {
      setUrlError('URL must start with https://');
    } else if (!isValidUrl(url)) {
      setUrlError('Please enter a valid URL');
    } else {
      setUrlError('');
    }
  };

  // Handle website data extraction
  const handleWebsiteScrape = async () => {
    if (!websiteUrl || !isValidUrl(websiteUrl)) return;

    setIsLoading(true);
    
    try {
      // First scrape the website
      const scrapeResponse = await getLocalApi().post('/scrape-website', {
        url: websiteUrl,
      });

      if (!scrapeResponse.status || scrapeResponse.status !== 200) {
        throw new Error('Failed to scrape website');
      }
      
      const scrapedData = scrapeResponse.data;
      
      // Then extract brand info from the scraped text
      const brandInfo = await extractBrandInfo(scrapedData.text);
      
      // Activate all fields before updating
      activateAllFields();
      
      // Update each field with extracted info
      Object.entries(brandInfo).forEach(([key, value]) => {
        // Special handling for brand_colors
        if (key === 'brand_colors' && Array.isArray(value) && value.length > 0) {
          // Transform the simple array of color hex values to BrandColorPalette format
          const formattedColors = [{
            name: 'Primary Colors',
            colors: (value as string[]).map(hex => ({
              name: hex,
              hex: hex.startsWith('#') ? hex : `#${hex}`
            }))
          }];
          
          handleSectionUpdate(key, formattedColors);
        } 
        // For other fields, handle normally
        else if (BRAND_DETAILS.some(field => field.key === key)) {
          handleSectionUpdate(key, value as string);
        }
      });
      
      // Auto-save if this is a new brand
      if (isNewBrand) {
        await createBrand();
      }
      
      setIsUrlModalOpen(false);
      setWebsiteUrl('');
      toast.success('Brand information extracted from website');
    } catch (error) {
      console.error('Error processing website:', error);
      toast.error('Failed to extract brand information from website');
    } finally {
      setIsLoading(false);
    }
  };

  // Determine which fields are available to add
  const availableFields = BRAND_DETAILS.filter(
    field => !activeFields.includes(field.key)
  );

  return (
    <div className="space-y-6">
      {/* Auto-fill options */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Auto-fill Brand Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              accept=".pdf"
              className="hidden"
            />
            <Button 
              variant="outline" 
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
              className="flex items-center"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <FileText className="mr-2 h-4 w-4" />
              )}
              Upload Brand Guide
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => setIsUrlModalOpen(true)}
              disabled={isLoading}
              className="flex items-center"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Globe className="mr-2 h-4 w-4" />
              )}
              Pull From Website
            </Button>
          </div>
          <p className="mt-2 text-xs text-muted-foreground">
            Let our AI extract brand details automatically from your PDF brand guide or company website.
          </p>
        </CardContent>
      </Card>
      
      {/* Website URL Modal */}
      <Dialog open={isUrlModalOpen} onOpenChange={setIsUrlModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enter Website URL</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="mb-4 text-sm text-muted-foreground">
              Enter your company website URL. We will analyze the content and extract relevant brand information.
            </p>
            <Input
              placeholder="https://example.com"
              value={websiteUrl}
              onChange={handleUrlChange}
              className={urlError ? 'border-red-500' : ''}
            />
            {urlError && (
              <p className="mt-1 text-sm text-red-500">{urlError}</p>
            )}
            <p className="mt-1 text-xs text-muted-foreground">
              URL must start with https://
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUrlModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleWebsiteScrape}
              disabled={!websiteUrl || !!urlError || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Extracting...
                </>
              ) : (
                'Extract Content'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Display active fields */}
      {BRAND_DETAILS.map(field => {
        // Only show field if it's active
        if (!activeFields.includes(field.key)) return null;

        return (
          <Card key={field.key} className="w-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{field.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <EditableSection
                title=""
                content={brand[field.key as keyof Brand] as string || ''}
                onSave={(content) => handleSectionUpdate(field.key, content)}
              />
              <div className="mt-1">
                <p className="text-xs text-muted-foreground">{field.description}</p>
              </div>
            </CardContent>
          </Card>
        );
      })}

      {/* Add field buttons */}
      {availableFields.length > 0 && (
        <div className="mt-4">
          <div className="flex flex-wrap gap-2">
            {availableFields.map(field => (
              <Button
                key={field.key}
                variant="outline"
                size="sm"
                onClick={() => addField(field.key)}
                className="flex items-center"
              >
                <Plus className="mr-1 h-4 w-4" />
                Add {field.title}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
