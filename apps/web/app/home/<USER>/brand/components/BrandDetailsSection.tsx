'use client';

import React from 'react';
import BrandSection from './BrandSection';
import BrandDetails from './BrandDetails';
import { useBrand } from './BrandContext';
import ErrorBoundary from './ErrorBoundary';

export default function BrandDetailsSection() {
  const { brand, updateBrandSection } = useBrand();
  
  return (
    <ErrorBoundary>
      <BrandSection
        title="Details"
        value="brand_details"
        id="details-section"
      >
        <BrandDetails
          brand={brand}
          handleSectionUpdate={updateBrandSection}
        />
      </BrandSection>
    </ErrorBoundary>
  );
}
