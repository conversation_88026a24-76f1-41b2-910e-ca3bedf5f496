'use client';

import React from 'react';
import BrandSection from './BrandSection';
import BrandColors from './color/BrandColors';
import { useBrand } from './BrandContext';
import { getTotalColorsCount } from './utilities/brandUtils';
import ErrorBoundary from './ErrorBoundary';

export default function BrandColorsSection() {
  const { 
    getBrandColors, 
    updateBrandSection, 
    handleDeletePalette 
  } = useBrand();
  
  const colors = getBrandColors();
  const totalColors = getTotalColorsCount(colors);
  
  return (
    <ErrorBoundary>
      <BrandSection
        title={`Colors (${totalColors})`}
        value="brand_colors"
        styleClass="brand_colors"
        id="colors-section"
      >
        <BrandColors
          colors={colors}
          onSave={(colors) => updateBrandSection('brand_colors', colors)}
          onDeletePalette={handleDeletePalette}
        />
      </BrandSection>
    </ErrorBoundary>
  );
}
